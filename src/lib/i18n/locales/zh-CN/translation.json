{"-1 for no limit, or a positive integer for a specific limit": "-1 表示无限制，正整数表示具体限制额度", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s'(秒), 'm'(分), 'h'(时), 'd'(天), 'w'(周) 或 '-1' 表示无过期时间", "(e.g. `sh webui.sh --api --api-auth username_password`)": "（例如：`sh webui.sh --api --api-auth username_password`）", "(e.g. `sh webui.sh --api`)": "（例如：`sh webui.sh --api`）", "(latest)": "（最新版）", "(leave blank for to use commercial endpoint)": "（留空以使用商用端点）", "[Last] dddd [at] h:mm A": "[上次] dddd [于] h:mm A", "[Today at] h:mm A": "[今天] h:mm A", "[Yesterday at] h:mm A": "[昨天] h:mm A", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} 个可用工具", "{{COUNT}} characters": "{{COUNT}} 个字符", "{{COUNT}} extracted lines": "已提取 {{COUNT}} 行", "{{COUNT}} hidden lines": "{{COUNT}} 行被隐藏", "{{COUNT}} Replies": "{{COUNT}} 条回复", "{{COUNT}} Sources": "{{COUNT}} 个引用来源", "{{COUNT}} words": "{{COUNT}} 个字", "{{LOCALIZED_DATE}} at {{LOCALIZED_TIME}}": "{{LOCALIZED_DATE}} {{LOCALIZED_TIME}}", "{{model}} download has been canceled": "已取消模型 {{model}} 的下载", "{{user}}'s Chats": "{{user}} 的对话记录", "{{webUIName}} Backend Required": "{{webUIName}} 需要后端服务", "*Prompt node ID(s) are required for image generation": "*图片生成需要提示词节点 ID", "1 Source": "1 个引用来源", "A new version (v{{LATEST_VERSION}}) is now available.": "新版本（v{{LATEST_VERSION}}）现已发布", "A task model is used when performing tasks such as generating titles for chats and web search queries": "任务模型用于执行生成对话标题和联网搜索查询等任务", "a user": "用户", "About": "关于", "Accept autocomplete generation / Jump to prompt variable": "接受自动补全内容 / 跳转到提示词变量", "Access": "访问", "Access Control": "访问控制", "Accessible to all users": "对所有用户开放", "Account": "账号", "Account Activation Pending": "账号待激活", "accurate": "准确", "Accurate information": "信息准确", "Action": "操作", "Action not found": "找不到对应的操作项", "Action Required for Chat Log Storage": "需要操作以保存对话记录", "Actions": "操作", "Activate": "激活", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "在对话框中输入 \"/{{COMMAND}}\" 激活此命令", "Active": "在线", "Active Users": "当前在线用户", "Add": "添加", "Add a model ID": "添加模型 ID", "Add a short description about what this model does": "添加有关该模型能力的简短描述", "Add a tag": "添加标签", "Add Arena Model": "添加竞技场模型", "Add Connection": "添加连接", "Add Content": "添加内容", "Add content here": "在此添加内容", "Add Custom Parameter": "增加自定义参数", "Add custom prompt": "添加自定义提示词", "Add Details": "丰富细节", "Add Files": "添加文件", "Add Group": "添加权限组", "Add Memory": "添加记忆", "Add Model": "添加模型", "Add Reaction": "添加表情", "Add Tag": "添加标签", "Add Tags": "添加标签", "Add text content": "添加文本内容", "Add User": "添加用户", "Add User Group": "添加权限组", "Additional Config": "额外配置项", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "Datalab Marker 的额外配置项，可以填写一个包含键值对的 JSON 字符串。例如：{\"key\": \"value\"}。支持的键包括：disable_links、keep_pageheader_in_output、keep_pagefooter_in_output、filter_blank_pages、drop_repeated_text、layout_coverage_threshold、merge_threshold、height_tolerance、gap_threshold、image_threshold、min_line_length、level_count 和 default_level。", "Additional Parameters": "额外参数", "Adjusting these settings will apply changes universally to all users.": "调整这些设置将会对所有用户生效", "admin": "管理员", "Admin": "管理员", "Admin Panel": "管理员面板", "Admin Settings": "管理员设置", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "管理员拥有所有工具的完全访问权限；用户则需在工作空间中为每个模型单独分配工具。", "Advanced Parameters": "高级参数", "Advanced Params": "高级参数", "AI": "AI", "All": "全部", "All chats have been unarchived.": "已成功将所有对话取消归档。", "All Documents": "所有文档", "All models deleted successfully": "所有模型删除成功", "Allow Call": "允许语音通话", "Allow Chat Controls": "允许使用对话高级设置", "Allow Chat Delete": "允许删除对话记录", "Allow Chat Deletion": "允许删除对话记录", "Allow Chat Edit": "允许编辑对话记录", "Allow Chat Export": "允许导出对话", "Allow Chat Params": "允许设置模型高级参数", "Allow Chat Share": "允许分享对话", "Allow Chat System Prompt": "允许设置系统提示词", "Allow Chat Valves": "允许修改工具和函数的配置项（Valves）", "Allow Continue Response": "允许继续生成回答", "Allow Delete Messages": "允许删除对话消息", "Allow File Upload": "允许上传文件", "Allow Multiple Models in Chat": "允许在对话中使用多个模型", "Allow non-local voices": "允许调用非本土音色", "Allow Rate Response": "允许对回答进行评价", "Allow Regenerate Response": "允许重新生成回答", "Allow Speech to Text": "允许语音转文本", "Allow Temporary Chat": "允许临时对话", "Allow Text to Speech": "允许文本转语音", "Allow User Location": "获取您的位置", "Allow Voice Interruption in Call": "允许语音通话时打断对话", "Allowed Endpoints": "允许的 API 端点", "Allowed File Extensions": "允许的文件扩展名", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "文件上传允许的扩展名。多个扩展名用逗号分隔。留空以允许所有文件类型。", "Already have an account?": "已拥有账号？", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "top_p 的替代方法，旨在平衡生成质量与多样性。参数 p 表示一个 token 被考虑的最低概率，该概率相对于最可能 token 的概率。例如，当 p=0.05 且最可能 token 的概率为 0.9 时，概率值低于 0.045 的 token 将被过滤掉。", "Always": "始终", "Always Collapse Code Blocks": "始终折叠代码块", "Always Expand Details": "始终展开详细信息", "Always Play Notification Sound": "始终播放通知声音", "Amazing": "很棒", "an assistant": "一个助手", "An error occurred while fetching the explanation": "获取解释时发生错误", "Analytics": "分析", "Analyzed": "已分析", "Analyzing...": "正在分析...", "and": "和", "and {{COUNT}} more": "还有 {{COUNT}} 个", "and create a new shared link.": "并创建新的分享链接", "Android": "Android", "API": "API", "API Base URL": "API 请求 URL", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "Datalab Marker API 服务的请求 URL。默认为：https://www.datalab.to/api/v1/marker", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "在图片描述中使用视觉语言模型的 API 详情。此参数不可与 picture_description_local 同时使用。", "API Key": "API 密钥", "API Key created.": "API 密钥已创建。", "API Key Endpoint Restrictions": "API 密钥端点限制", "API keys": "API 密钥", "API Version": "API 版本", "API Version is required": "API 版本是必填项。", "Application DN": "Application DN", "Application DN Password": "Application DN 密码", "applies to all users with the \"user\" role": "用于所有具有“用户”角色的用户", "April": "四月", "Archive": "归档", "Archive All Chats": "归档所有对话记录", "Archived Chats": "已归档对话", "archived-chat-export": "导出已归档对话", "Are you sure you want to clear all memories? This action cannot be undone.": "您确认要清除所有记忆吗？清除后无法还原。", "Are you sure you want to delete this channel?": "您确认要删除此频道吗？", "Are you sure you want to delete this message?": "您确认要删除此消息吗？", "Are you sure you want to unarchive all archived chats?": "您确认要取消所有已归档的对话吗？", "Are you sure?": "您确认吗？", "Arena Models": "启用竞技场匿名评价模型", "Artifacts": "产物", "Ask": "提问", "Ask a question": "提问", "Assistant": "助手", "Attach file from knowledge": "从知识库中选择文件", "Attach Knowledge": "引用知识库", "Attach Notes": "引用笔记", "Attach Webpage": "引用网页", "Attention to detail": "注重细节", "Attribute for Mail": "邮箱属性", "Attribute for Username": "用户名属性", "Audio": "语音", "August": "八月", "Auth": "认证方式", "Authenticate": "认证", "Authentication": "身份验证", "Auto": "自动", "Auto-Copy Response to Clipboard": "自动复制回答内容到剪贴板", "Auto-playback response": "自动朗读回复内容", "Autocomplete Generation": "输入框内容自动补全", "Autocomplete Generation Input Max Length": "输入框内容自动补全的最大字符数限制", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 API 鉴权字符串", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 URL 是必填项。", "Available list": "可用列表", "Available Tools": "可用工具", "available users": "可用用户", "available!": "版本可用！", "Away": "离开", "Awful": "糟糕", "Azure AI Speech": "Azure AI 语音", "Azure OpenAI": "Azure OpenAI", "Azure Region": "Azure 区域", "Back": "返回", "Bad Response": "点踩此回答", "Banners": "公告横幅", "Base Model (From)": "基础模型（来自）", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "仅在启动或保存设置时获取基础模型列表以提升访问速度，但显示的模型列表可能不是最新的。", "Bearer": "密钥（Bearer）", "before": "之前", "Being lazy": "回答不完整或敷衍了事", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing 搜索 V7 端点", "Bing Search V7 Subscription Key": "Bing 搜索 V7 订阅密钥", "Bio": "个人简介", "Birth Date": "出生日期", "BM25 Weight": "BM25 混合搜索权重", "Bocha Search API Key": "Bocha Search API 密钥", "Bold": "粗体", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "为受限响应提升或惩罚特定标记。偏置值将被限制在 -100 到 100（包括两端）之间。（默认：无）", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "必需同时提供 Docling 文字识别引擎和所需语言，或者都留空。", "Brave Search API Key": "Brave Search API 密钥", "Bullet List": "无序列表", "Button ID": "按钮 ID", "Button Label": "按钮文本", "Button Prompt": "点击按钮后执行的提示词", "By {{name}}": "由 {{name}} 提供", "Bypass Embedding and Retrieval": "绕过嵌入和检索", "Bypass Web Loader": "绕过网页加载器", "Cache Base Model List": "缓存基础模型列表", "Calendar": "日历", "Call": "语音通话", "Call feature is not supported when using Web STT engine": "使用 Web 语音转文字引擎时不支持语音通话功能", "Camera": "摄像头", "Cancel": "取消", "Capabilities": "能力", "Capture": "截图", "Capture Audio": "录制音频", "Certificate Path": "证书路径", "Change Password": "更改密码", "Channel": "频道", "Channel deleted successfully": "删除频道成功", "Channel Name": "频道名称", "Channel updated successfully": "更新频道成功", "Channels": "频道", "Character": "字符", "Character limit for autocomplete generation input": "输入框内容自动补全输入的字符限制", "Chart new frontiers": "开辟前沿", "Chat": "对话", "Chat Background Image": "对话背景图片", "Chat Bubble UI": "以聊天气泡的形式显示对话内容", "Chat Controls": "对话高级设置", "Chat Conversation": "对话内容", "Chat direction": "对话显示方向", "Chat ID": "对话 ID", "Chat moved successfully": "移动对话成功", "Chat Overview": "对话概述", "Chat Permissions": "对话权限", "Chat Tags Auto-Generation": "自动生成对话标签", "Chats": "对话", "Check Again": "刷新重试", "Check for updates": "检查更新", "Checking for updates...": "正在检查更新...", "Choose a model before saving...": "保存前选择一个模型...", "Chunk Overlap": "块重叠 (<PERSON><PERSON>)", "Chunk Size": "块大小 (Chunk Size)", "Ciphers": "加密算法 (Ciphers)", "Citation": "引文", "Citations": "引用", "Clear memory": "清除记忆", "Clear Memory": "清除记忆", "click here": "点击此处", "Click here for filter guides.": "点击此处查看筛选指南", "Click here for help.": "点击此处获取帮助", "Click here to": "点击", "Click here to download user import template file.": "点击此处下载用户导入所需的模板文件", "Click here to learn more about faster-whisper and see the available models.": "点击此处了解更多关于 faster-whisper 的信息，并查看可用的模型", "Click here to see available models.": "点击此处查看可用模型", "Click here to select": "点击此处选择", "Click here to select a csv file.": "点击此处选择 csv 文件", "Click here to select a py file.": "点击此处选择 py 文件", "Click here to upload a workflow.json file.": "点击此处上传 workflow.json 文件", "click here.": "点击此处", "Click on the user role button to change a user's role.": "点击用户角色按钮以更改用户的角色", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "写入剪贴板时被拒绝。请检查浏览器设置，授予必要权限。", "Clone": "复制", "Clone Chat": "克隆对话", "Clone of {{TITLE}}": "{{TITLE}} 的副本", "Close": "关闭", "Close Banner": "关闭横幅", "Close Configure Connection Modal": "关闭外部连接配置弹窗", "Close modal": "关闭弹窗", "Close settings modal": "关闭设置弹窗", "Close Sidebar": "收起侧边栏", "CMU ARCTIC speaker embedding name": "CMU ARCTIC 朗读者嵌入名（Speaker Embedding Name）", "Code Block": "代码块", "Code execution": "代码执行", "Code Execution": "代码执行", "Code Execution Engine": "代码执行引擎", "Code Execution Timeout": "代码执行超时", "Code formatted successfully": "代码格式化成功", "Code Interpreter": "代码解释器", "Code Interpreter Engine": "代码解释引擎", "Code Interpreter Prompt Template": "代码解释器提示词模板", "Collapse": "折叠", "Collection": "文件集", "Color": "颜色", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API 密钥", "ComfyUI Base URL": "ComfyUI URL", "ComfyUI Base URL is required.": "ComfyUI URL 是必填项。", "ComfyUI Workflow": "ComfyUI 工作流", "ComfyUI Workflow Nodes": "ComfyUI 工作流节点", "Comma separated Node Ids (e.g. 1 or 1,2)": "使用英文逗号分隔的节点 ID（例如 1 或 1,2）", "Command": "命令", "Comment": "注释", "Completions": "续写", "Compress Images in Channels": "压缩频道中的图片", "Concurrent Requests": "并发请求", "Config imported successfully": "配置导入成功", "Configure": "配置", "Confirm": "确认", "Confirm Password": "确认密码", "Confirm your action": "确认要继续吗？", "Confirm your new password": "确认新密码", "Confirm Your Password": "确认您的密码", "Connect to your own OpenAI compatible API endpoints.": "连接到符合 OpenAI 接口格式的 API 端点", "Connect to your own OpenAPI compatible external tool servers.": "连接到符合 OpenAPI 规范的外部工具服务器", "Connection failed": "连接失败", "Connection successful": "连接成功", "Connection Type": "连接类型", "Connections": "外部连接", "Connections saved successfully": "连接保存成功", "Connections settings updated": "连接设置已更新", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "限制推理模型的推理努力（reasoning effort）。仅适用于支持推理努力控制的特定提供商的推理模型。", "Contact Admin for WebUI Access": "请联系管理员以获取访问权限", "Content": "内容", "Content Extraction Engine": "内容提取引擎", "Continue Response": "继续生成", "Continue with {{provider}}": "使用 {{provider}} 继续", "Continue with Email": "使用邮箱登录", "Continue with LDAP": "使用 LDAP 登录", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "设置消息文本拆分方法，用于 TTS 请求。“Punctuation”拆分为句子，“paragraphs”拆分为段落，“none”将消息保留为单个字符串。", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "设置生成文本中标记序列的重复度。较高的值（例如 1.5）表示严厉惩罚重复，而较低的值（例如 1.1）则表示相对宽松。当值为 1 时，此功能将被禁用。", "Controls": "对话高级设置", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "控制输出文本中连贯性和多样性之间的平衡度。数值越低，生成的文本越聚焦和连贯。", "Conversation saved successfully": "对话保存成功", "Copied": "已复制", "Copied link to clipboard": "已复制链接到剪贴板", "Copied shared chat URL to clipboard!": "已复制对话的分享链接到剪贴板！", "Copied to clipboard": "已复制到剪贴板", "Copy": "复制", "Copy Formatted Text": "复制文本时包含特殊格式", "Copy last code block": "复制最后一个代码块中的代码", "Copy last response": "复制最后一次回复内容", "Copy link": "复制链接", "Copy Link": "复制链接", "Copy to clipboard": "复制到剪贴板", "Copying to clipboard was successful!": "成功复制到剪贴板！", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "为允许 Open WebUI 发出的请求，提供商必须正确配置 CORS", "Create": "创建", "Create a knowledge base": "创建知识库", "Create a model": "创建一个模型", "Create Account": "创建账号", "Create Admin Account": "创建管理员账号", "Create Channel": "创建频道", "Create Folder": "创建分组", "Create Group": "创建权限组", "Create Knowledge": "创建知识", "Create Model": "", "Create new key": "创建新密钥", "Create new secret key": "创建新安全密钥", "Create Note": "创建笔记", "Create your first note by clicking on the plus button below.": "点击下面的加号按钮创建您的第一个笔记", "Created at": "创建于", "Created At": "创建于", "Created by": "作者", "Created by you": "", "CSV Import": "通过 CSV 文件导入", "Ctrl+Enter to Send": "Ctrl+Enter 发送", "Current Model": "当前模型", "Current Password": "当前密码", "Custom": "自定义", "Custom description enabled": "自定义描述已启用", "Custom Parameter Name": "自定义参数名称", "Custom Parameter Value": "自定义参数值", "Danger Zone": "危险区域", "Dark": "暗色", "Data Controls": "数据", "Database": "数据库", "Datalab Marker API": "Datalab Marker API", "Datalab Marker API Key required.": "需要 Datalab Marker API 密钥", "DD/MM/YYYY": "DD/MM/YYYY", "December": "十二月", "Deepgram": "Deepgram", "Default": "默认", "Default (Open AI)": "默认 (OpenAI)", "Default (SentenceTransformers)": "默认 (SentenceTransformers)", "Default action buttons will be used.": "已启用默认的快捷操作按钮。", "Default description enabled": "默认描述已启用", "Default Features": "默认功能", "Default Filters": "默认过滤器", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "“默认”模式会在请求模型前调用工具，因此能兼容更多模型。“原生”模式则依赖模型本身的工具调用能力，但需要模型本身支持该功能。", "Default Model": "默认模型", "Default model updated": "默认模型已更新", "Default Models": "默认模型", "Default permissions": "默认权限", "Default permissions updated successfully": "默认权限更新成功", "Default Prompt Suggestions": "默认提示词建议", "Default to 389 or 636 if TLS is enabled": "启用 TLS 将默认使用 389 或 636 端口", "Default to ALL": "默认为：ALL", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "默认进行分段检索以提取重点和相关内容（推荐）", "Default User Role": "默认用户角色", "Delete": "删除", "Delete a model": "删除模型", "Delete All Chats": "删除所有对话记录", "Delete All Models": "删除所有模型", "Delete chat": "删除对话记录", "Delete Chat": "删除对话记录", "Delete chat?": "要删除此对话记录吗？", "Delete folder?": "要删除此分组吗？", "Delete function?": "要删除此函数吗？", "Delete Message": "删除消息", "Delete message?": "要删除此消息吗？", "Delete Model": "", "Delete note?": "要删除此笔记吗？", "Delete prompt?": "要删除此提示词吗？", "delete this link": "此处删除这个链接", "Delete tool?": "要删除此工具吗？", "Delete User": "删除用户", "Deleted {{deleteModelTag}}": "已删除 {{deleteModelTag}}", "Deleted {{name}}": "已删除 {{name}}", "Deleted User": "已删除用户", "Deployment names are required for Azure OpenAI": "Azure OpenAI 需要部署名称", "Describe Pictures in Documents": "描述文档中的图片", "Describe your knowledge base and objectives": "描述您的知识库和目标", "Description": "描述", "Detect Artifacts Automatically": "自动检测对话产物", "Dictate": "语音输入", "Didn't fully follow instructions": "没有完全遵循指令", "Direct": "直接", "Direct Connections": "直接连接", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "直接连接允许用户连接自有的 OpenAI 兼容的 API 端点", "Direct Tool Servers": "直接连接工具服务器", "Directory selection was cancelled": "已取消选择目录", "Disable Code Interpreter": "禁用代码解释器", "Disable Image Extraction": "禁用图像提取", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "禁用从 PDF 中提取图像。若启用“使用大语言模型（LLM）”，图像将自动添加描述。默认为关闭", "Disabled": "已禁用", "Discover a function": "发现更多函数", "Discover a model": "发现更多模型", "Discover a prompt": "发现更多提示词", "Discover a tool": "发现更多工具", "Discover how to use Open WebUI and seek support from the community.": "了解如何使用 Open WebUI 并寻求社区支持", "Discover wonders": "追寻奇迹", "Discover, download, and explore custom functions": "发现、下载并探索更多自定义函数", "Discover, download, and explore custom prompts": "发现、下载并探索更多自定义提示词", "Discover, download, and explore custom tools": "发现、下载并探索更多自定义工具", "Discover, download, and explore model presets": "发现、下载并探索更多模型预设", "Display": "显示", "Display chat title in tab": "在浏览器标签页中显示对话标题", "Display Emoji in Call": "在通话中显示 Emoji", "Display Multi-model Responses in Tabs": "以标签页的形式展示多个模型的回答", "Display the username instead of You in the Chat": "在对话中显示用户名而不是“你”", "Displays citations in the response": "在回答中显示引用来源", "Displays status updates (e.g., web search progress) in the response": "在回答中显示实时状态信息（例如：网络搜索进度）", "Dive into knowledge": "纵览知识", "dlparse_v1": "dlparse_v1", "dlparse_v2": "dlparse_v2", "dlparse_v4": "dlparse_v4", "Do not install functions from sources you do not fully trust.": "切勿安装不可信来源的函数", "Do not install tools from sources you do not fully trust.": "切勿安装不可信来源的工具", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "需要提供 Docling 服务器 URL", "Document": "文档", "Document Intelligence": "Document Intelligence", "Document Intelligence endpoint required.": "Document Intelligence 接口端点是必填项。", "Documentation": "帮助文档", "Documents": "文档", "does not make any external connections, and your data stays securely on your locally hosted server.": "不会与外部建立任何连接，您的数据会安全地存储在本地托管的服务器上。", "Domain Filter List": "域名过滤列表", "don't fetch random pipelines from sources you don't trust.": "请勿从未经验证或不可信的来源获取 Pipelines。", "Don't have an account?": "没有账号？", "don't install random functions from sources you don't trust.": "切勿从未经验证或不可信的来源安装函数", "don't install random tools from sources you don't trust.": "切勿从未经验证或不可信的来源安装工具", "Don't like the style": "不喜欢这种文风", "Done": "完成", "Download": "下载", "Download & Delete": "下载并删除", "Download as SVG": "下载为 SVG", "Download canceled": "下载已取消", "Download Database": "下载数据库", "Drag and drop a file to upload or select a file to view": "拖动文件上传或选择文件查看", "Draw": "平局", "Drop any files here to upload": "拖拽文件至此上传", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "例如：“30s”,“10m”。有效的时间单位包括：“s”(秒), “m”(分), “h”(时)", "e.g. \"json\" or a JSON schema": "例如：\"json\" 或一个 JSON schema", "e.g. 60": "例如：60", "e.g. A filter to remove profanity from text": "例如：一个用于剔除文本中不当内容的过滤器", "e.g. en": "例如：en", "e.g. My Filter": "例如：我的过滤器", "e.g. My Tools": "例如：我的工具", "e.g. my_filter": "例如：我的过滤器", "e.g. my_tools": "例如：我的工具", "e.g. pdf, docx, txt": "例如：pdf,docx,txt", "e.g. Tools for performing various operations": "例如：用于执行各种操作的工具", "e.g., 3, 4, 5 (leave blank for default)": "例如：3,4,5（留空使用默认值）", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "例如：audio/wav,audio/mpeg,video/*（留空使用默认值）", "e.g., en-US,ja-JP (leave blank for auto-detect)": "例如：en-US,ja-JP（留空则自动检测）", "e.g., westus (leave blank for eastus)": "例如：westus（留空则默认为 eastus）", "Edit": "编辑", "Edit Arena Model": "编辑竞技场模型", "Edit Channel": "编辑频道", "Edit Connection": "编辑连接", "Edit Default Permissions": "编辑默认权限", "Edit Folder": "编辑分组", "Edit Memory": "编辑记忆", "Edit User": "编辑用户", "Edit User Group": "编辑用户组", "edited": "已编辑", "Edited": "已编辑", "Editing": "编辑中", "Eject": "弹出", "ElevenLabs": "ElevenLabs", "Email": "电子邮箱", "Embark on adventures": "破界远航", "Embedding": "嵌入", "Embedding Batch Size": "嵌入层批处理大小 (Embedding <PERSON><PERSON> Size)", "Embedding Model": "嵌入模型", "Embedding Model Engine": "嵌入模型引擎", "Embedding model set to \"{{embedding_model}}\"": "嵌入模型设置为 \"{{embedding_model}}\"", "Enable API Key": "启用 API 密钥", "Enable autocomplete generation for chat messages": "启用对话输入框内容自动补全", "Enable Code Execution": "启用代码执行", "Enable Code Interpreter": "启用代码解释器", "Enable Community Sharing": "启用分享至社区", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "启用内存锁定 (mlock)，防止模型数据被移出内存。此选项将模型的工作集页面锁定在内存中，确保它们不会被交换到磁盘，避免页面错误，确保快速数据访问，从而维持性能。", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "启用内存映射 (mmap) 加载模型数据。此选项将磁盘文件视作内存中的数据，允许系统使用磁盘存储作为内存的扩展，通过更快的数据访问来提高模型性能。然而，它可能无法在所有系统上正常工作，并且可能会消耗大量磁盘空间。", "Enable Message Rating": "启用模型回答结果评价", "Enable Mirostat sampling for controlling perplexity.": "启用 Mirostat 采样以控制困惑度", "Enable New Sign Ups": "允许新用户注册", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "启用、禁用或自定义模型使用的推理过程标签。“启用”表示使用默认标签，“禁用”将不识别推理标签，“自定义”可指定起始和闭合标签。", "Enabled": "已启用", "End Tag": "结束标签", "Endpoint URL": "端点 URL", "Enforce Temporary Chat": "强制临时对话", "Enhance": "润色", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "确保您的 CSV 文件按以下顺序包含 4 列：姓名、电子邮箱、密码、角色。", "Enter {{role}} message here": "在此处输入 {{role}} 的对话内容", "Enter a detail about yourself for your LLMs to recall": "输入关于您的详细信息，以便大语言模型记住这些内容。", "Enter a title for the pending user info overlay. Leave empty for default.": "输入用户待激活界面的标题。留空使用默认", "Enter a watermark for the response. Leave empty for none.": "输入复制水印。留空则不添加", "Enter additional headers in JSON format": "", "Enter additional headers in JSON format (e.g. {{'{{\"X-Custom-Header\": \"value\"}}'}})": "", "Enter additional parameters in JSON format": "", "Enter api auth string (e.g. username:password)": "输入 API 鉴权路径（例如：用户名:密码）", "Enter Application DN": "输入 Application DN", "Enter Application DN Password": "输入 Application DN 密码", "Enter Bing Search V7 Endpoint": "输入 Bing Search V7 端点", "Enter Bing Search V7 Subscription Key": "输入 Bing Search V7 订阅密钥", "Enter Bocha Search API Key": "输入 Bocha Search API 密钥", "Enter Brave Search API Key": "输入 Brave Search API 密钥", "Enter certificate path": "输入证书路径", "Enter CFG Scale (e.g. 7.0)": "输入 CFG Scale（例如：7.0）", "Enter Chunk Overlap": "输入块重叠 (<PERSON><PERSON>lap)", "Enter Chunk Size": "输入块大小 (Chunk Size)", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "输入以逗号分隔的“token:bias_value”对（例如：5432:100, 413:-100）", "Enter Config in JSON format": "输入 JSON 格式的配置", "Enter content for the pending user info overlay. Leave empty for default.": "输入用户待激活界面的内容。留空使用默认", "Enter coordinates (e.g. 51.505, -0.09)": "输入坐标经纬度（例如：51.505, -0.09）", "Enter Datalab Marker API Base URL": "输入 Datalab Marker API 请求 URL", "Enter Datalab Marker API Key": "输入 Datalab Marker API 密钥", "Enter description": "输入简介描述", "Enter Docling OCR Engine": "输入 Docling 文字识别引擎", "Enter Docling OCR Language(s)": "输入 Docling 文字识别语言", "Enter Docling Server URL": "输入 Docling 服务器 URL", "Enter Document Intelligence Endpoint": "输入 Document Intelligence 端点", "Enter Document Intelligence Key": "输入 Document Intelligence 密钥", "Enter domains separated by commas (e.g., example.com,site.org)": "输入域名，多个域名用逗号分隔（例如：example.com,site.org）", "Enter Exa API Key": "输入 Exa API 密钥", "Enter External Document Loader API Key": "输入外部文档加载器 API 密钥", "Enter External Document Loader URL": "输入外部文档加载器 URL", "Enter External Web Loader API Key": "输入外部网页加载器 API 密钥", "Enter External Web Loader URL": "输入外部网页加载器 URL", "Enter External Web Search API Key": "输入外部联网搜索 API 密钥", "Enter External Web Search URL": "输入外部联网搜索 URL", "Enter Firecrawl API Base URL": "输入 Firecrawl API URL", "Enter Firecrawl API Key": "输入 Firecrawl API 密钥", "Enter folder name": "输入分组名称", "Enter Github Raw URL": "输入 Github Raw URL", "Enter Google PSE API Key": "输入 Google PSE API 密钥", "Enter Google PSE Engine Id": "输入 Google PSE 引擎 ID", "Enter hex color (e.g. #FF0000)": "输入十六进制颜色（例如：#FF0000）", "Enter ID": "输入 ID", "Enter Image Size (e.g. 512x512)": "输入图像分辨率（例如：512x512）", "Enter Jina API Key": "输入 Jina API 密钥", "Enter JSON config (e.g., {\"disable_links\": true})": "输入 JSON 配置（例如：{\"disable_links\": true}）", "Enter Jupyter Password": "输入 Ju<PERSON>ter 密码", "Enter Jupyter Token": "输入 Ju<PERSON>ter 令牌", "Enter Jupyter URL": "输入 Jupyter URL", "Enter Kagi Search API Key": "输入 Kagi Search API 密钥", "Enter Key Behavior": "Enter 键行为", "Enter language codes": "输入语言代码", "Enter Mistral API Key": "输入 Mistral API 密钥", "Enter Model ID": "输入模型 ID", "Enter model tag (e.g. {{modelTag}})": "输入模型标签（例如：{{modelTag}}）", "Enter Mojeek Search API Key": "输入 Mojeek Search API 密钥", "Enter name": "输入名称", "Enter New Password": "输入新密码", "Enter Number of Steps (e.g. 50)": "输入步骤数 (Steps)（例如：50）", "Enter Ollama Cloud API Key": "输入 Ollama Cloud API 密钥", "Enter Perplexity API Key": "输入 Perplexity API 密钥", "Enter Playwright Timeout": "输入 Playwright 超时时间", "Enter Playwright WebSocket URL": "输入 Playwright WebSocket URL", "Enter proxy URL (e.g. **************************:port)": "输入代理 URL（例如：https://用户名:密码@主机名:端口）", "Enter reasoning effort": "输入推理努力", "Enter Sampler (e.g. Euler a)": "输入 Sampler（例如：Euler a）", "Enter Scheduler (e.g. Karras)": "输入 Scheduler（例如：Karras）", "Enter Score": "输入评分", "Enter SearchApi API Key": "输入 SearchApi API 密钥", "Enter SearchApi Engine": "输入 SearchApi 引擎", "Enter Searxng Query URL": "输入 Searxng 查询 URL", "Enter Seed": "输入 Seed", "Enter SerpApi API Key": "输入 SerpApi API 密钥", "Enter SerpApi Engine": "输入 SerpApi 引擎", "Enter Serper API Key": "输入 Serper API 密钥", "Enter Serply API Key": "输入 Serply API 密钥", "Enter Serpstack API Key": "输入 Serpstack API 密钥", "Enter server host": "输入服务器主机名", "Enter server label": "输入服务器标签", "Enter server port": "输入服务器端口", "Enter Sougou Search API sID": "输入搜狗搜索 API Secret ID", "Enter Sougou Search API SK": "输入搜狗搜索 API Secret 密钥", "Enter stop sequence": "输入停止序列 (Stop Sequence)", "Enter system prompt": "输入系统提示词", "Enter system prompt here": "在这里输入系统提示词", "Enter Tavily API Key": "输入 Tavily API 密钥", "Enter Tavily Extract Depth": "输入 Tavily 提取深度", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "输入 WebUI 的公共 URL。此 URL 将用于在通知中生成链接", "Enter the URL of the function to import": "输入要导入函数的 URL", "Enter the URL to import": "输入要导入的 URL", "Enter Tika Server URL": "输入 Tika 服务器 URL", "Enter timeout in seconds": "输入以秒为单位的超时时间", "Enter to Send": "Enter 键发送", "Enter Top K": "输入 Top K", "Enter Top K Reranker": "输入 Top K Reranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "输入 URL（例如：http://127.0.0.1:7860/）", "Enter URL (e.g. http://localhost:11434)": "输入 URL（例如：http://localhost:11434）", "Enter value": "输入值", "Enter value (true/false)": "输入布尔值（true 或 false）", "Enter Yacy Password": "输入 YaCy 密码", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "输入 YaCy URL（例如：http://yacy.example.com:8090）", "Enter Yacy Username": "输入 YaCy 用户名", "Enter your code here...": "在此输入您的代码…", "Enter your current password": "输入当前密码", "Enter Your Email": "输入您的电子邮箱", "Enter Your Full Name": "输入您的名称", "Enter your gender": "输入您的性别", "Enter your message": "输入您的消息", "Enter your name": "输入您的名称", "Enter Your Name": "输入您的名称", "Enter your new password": "输入您的新密码", "Enter Your Password": "输入您的密码", "Enter Your Role": "输入您的权限组", "Enter Your Username": "输入您的用户名", "Enter your webhook URL": "输入您的 Webhook URL", "Entra ID": "Entra ID", "Error": "错误", "ERROR": "错误", "Error accessing directory": "访问目录时出错", "Error accessing Google Drive: {{error}}": "访问 Google 云端硬盘时出错：{{error}}", "Error accessing media devices.": "访问媒体设备时出错。", "Error starting recording.": "开始录制时出错。", "Error unloading model: {{error}}": "卸载模型时出错：{{error}}", "Error uploading file: {{error}}": "上传文件时出错：{{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "错误：ID 为“{{modelId}}”的模型已存在。请选择不同的模型 ID。", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "错误：模型 ID 不能为空。请输入有效的模型 ID。", "Evaluations": "竞技场评估", "Everyone": "所有人", "Exa API Key": "Exa API 密钥", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "例如：(&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "例如：ALL", "Example: mail": "例如：mail", "Example: ou=users,dc=foo,dc=example": "例如：ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "例如：sAMAccountName 或 uid 或 userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "已达到最大授权人数，请联系支持人员提升授权人数", "Exclude": "排除", "Execute code for analysis": "执行代码进行分析", "Executing **{{NAME}}**...": "正在执行 **{{NAME}}**...", "Expand": "展开", "Experimental": "实验性", "Explain": "解释", "Explore the cosmos": "探索星辰", "Export": "导出", "Export All Archived Chats": "导出所有已存档对话", "Export All Chats (All Users)": "导出所有用户对话", "Export chat (.json)": "JSON 文件 (.json)", "Export Chats": "导出对话", "Export Config to JSON File": "将配置信息导出为 JSON 文件", "Export Presets": "导出预设", "Export Prompt Suggestions": "导出提示词建议", "Export to CSV": "导出到 CSV", "Export Users": "导出所有用户信息", "External": "外部", "External Document Loader URL required.": "需要外部文档加载器 URL", "External Task Model": "外部任务模型", "External Tools": "外部工具", "External Web Loader API Key": "外部网页加载器 API 密钥", "External Web Loader URL": "外部网页加载器 URL", "External Web Search API Key": "外部联网搜索 API 密钥", "External Web Search URL": "外部联网搜索 URL", "Fade Effect for Streaming Text": "流式输出内容时启用动态渐显效果", "Failed to add file.": "添加文件失败", "Failed to connect to {{URL}} OpenAPI tool server": "连接到 {{URL}} OpenAPI 工具服务器失败", "Failed to copy link": "复制链接失败", "Failed to create API Key.": "创建 API 密钥失败", "Failed to delete note": "删除笔记失败", "Failed to extract content from the file: {{error}}": "文件内容提取失败：{{error}}", "Failed to extract content from the file.": "文件内容提取失败", "Failed to fetch models": "获取模型失败", "Failed to generate title": "生成标题失败", "Failed to import models": "导入模型配置失败", "Failed to load chat preview": "对话预览加载失败", "Failed to load file content.": "文件内容加载失败", "Failed to move chat": "移动对话失败", "Failed to read clipboard contents": "读取剪贴板内容失败", "Failed to render diagram": "", "Failed to save connections": "保存连接失败", "Failed to save conversation": "保存对话失败", "Failed to save models configuration": "保存模型配置失败", "Failed to update settings": "更新设置失败", "Failed to upload file.": "上传文件失败", "fast": "快速", "Features": "功能", "Features Permissions": "功能权限", "February": "二月", "Feedback Details": "反馈详情", "Feedback History": "历史反馈", "Feedbacks": "反馈", "Feel free to add specific details": "欢迎补充具体细节", "Female": "女性", "File": "文件", "File added successfully.": "文件成功添加", "File content updated successfully.": "文件内容成功更新", "File Mode": "文件模式", "File not found.": "文件未找到。", "File removed successfully.": "文件成功删除", "File size should not exceed {{maxSize}} MB.": "文件大小不应超过 {{maxSize}} MB", "File Upload": "文件上传", "File uploaded successfully": "文件上传成功", "Files": "文件", "Filter": "过滤", "Filter is now globally disabled": "过滤器已全局禁用", "Filter is now globally enabled": "过滤器已全局启用", "Filters": "过滤器", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "检测到浏览器指纹伪造插件：无法使用姓名缩写作为头像，将使用系统默认的头像。", "Firecrawl API Base URL": "Firecrawl API URL", "Firecrawl API Key": "Firecrawl API 密钥", "Floating Quick Actions": "快捷操作浮窗", "Focus chat input": "激活对话输入框", "Folder": "", "Folder Background Image": "分组背景图", "Folder deleted successfully": "分组删除成功", "Folder Name": "分组名称", "Folder name cannot be empty.": "分组名称不能为空", "Folder name updated successfully": "分组名称更新成功", "Folder updated successfully": "分组更新成功", "Folders": "分组", "Follow up": "追问", "Follow Up Generation": "追问生成", "Follow Up Generation Prompt": "追问生成提示词", "Follow-Up Auto-Generation": "自动生成追问", "Followed instructions perfectly": "完全遵循指令", "Force OCR": "强制文字识别", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "强制识别 PDF 所有的页面文字。若 PDF 中包含清晰且可直接复制的文本内容，该功能可能会降低识别准确率。默认为关闭", "Forge new paths": "开辟新境", "Form": "手动创建", "Format Lines": "行内容格式化", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "对输出中的文本行进行格式处理。默认为 False。设置为 True 时，会对所有文本行的内容进行格式化，检测并识别行内的数学公式和样式。", "Format your variables using brackets like this:": "使用括号格式化您的变量，如下所示：", "Formatting may be inconsistent from source.": "格式可能会与原始文件不完全一致。", "Forwards system user OAuth access token to authenticate": "转发用户的 OAuth 访问令牌（Access Token）以进行身份验证", "Forwards system user session credentials to authenticate": "转发用户的会话凭证（Session Credentials）以进行身份验证", "Full Context Mode": "完整上下文模式", "Function": "函数", "Function Calling": "函数调用 (Function Calling)", "Function created successfully": "函数创建成功", "Function deleted successfully": "函数删除成功", "Function Description": "函数描述", "Function ID": "函数 ID", "Function imported successfully": "函数导入成功", "Function is now globally disabled": "函数全局已禁用", "Function is now globally enabled": "函数全局已启用", "Function Name": "函数名称", "Function updated successfully": "函数更新成功", "Functions": "函数", "Functions allow arbitrary code execution.": "注意：函数有权执行任意代码", "Functions imported successfully": "函数导入成功", "Gemini": "Gemini", "Gemini API Config": "Gemini API 配置", "Gemini API Key is required.": "Gemini API 密钥是必填项。", "Gender": "性别", "General": "通用", "Generate": "生成", "Generate an image": "生成图像", "Generate Image": "生成图像", "Generate prompt pair": "生成提示对", "Generated Image": "已生成图像", "Generating search query": "生成搜索查询", "Generating...": "生成中...", "Get information on {{name}} in the UI": "在界面中获取 {{name}} 的信息", "Get started": "开始使用", "Get started with {{WEBUI_NAME}}": "开始使用 {{WEBUI_NAME}}", "Global": "全局", "Good Response": "点赞此回答", "Google Drive": "Google 云端硬盘", "Google PSE API Key": "Google PSE API 密钥", "Google PSE Engine Id": "Google PSE 引擎 ID", "Gravatar": "Gravatar 头像", "Group": "权限组", "Group created successfully": "权限组创建成功", "Group deleted successfully": "权限组删除成功", "Group Description": "权限组描述", "Group Name": "权限组名称", "Group updated successfully": "权限组更新成功", "Groups": "权限组", "H1": "一级标题", "H2": "二级标题", "H3": "三级标题", "Haptic Feedback": "震动反馈", "Headers": "", "Headers must be a valid JSON object": "", "Height": "高度", "Hello, {{name}}": "您好，{{name}}", "Help": "帮助", "Help us create the best community leaderboard by sharing your feedback history!": "分享您的反馈历史记录，共建最佳模型社区排行榜！", "Hex Color": "十六进制颜色代码", "Hex Color - Leave empty for default color": "十六进制颜色代码 - 留空使用默认颜色", "Hide": "隐藏", "Hide from Sidebar": "从侧边栏隐藏", "Hide Model": "隐藏模型", "High": "高", "High Contrast Mode": "高对比度模式", "Home": "主页", "Host": "主机", "How can I help you today?": "有什么我能帮您的吗？", "How would you rate this response?": "您如何评价这个回答？", "HTML": "HTML", "Hybrid Search": "混合搜索", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "我已阅读并理解我的行为所带来的影响，知悉执行任意代码的风险，并确认代码来源可信。", "ID": "ID", "ID cannot contain \":\" or \"|\" characters": "ID 中不允许包含 “:” 或 “|” 字符", "iframe Sandbox Allow Forms": "iframe 沙盒允许表单提交", "iframe Sandbox Allow Same Origin": "iframe 沙盒允许同源访问", "Ignite curiosity": "点燃求知", "Image": "图像生成", "Image Compression": "压缩图像", "Image Compression Height": "压缩图像高度", "Image Compression Width": "压缩图像宽度", "Image Generation": "图像生成", "Image Generation (Experimental)": "图像生成（实验性）", "Image Generation Engine": "图像生成引擎", "Image Max Compression Size": "压缩图像后最大分辨率", "Image Max Compression Size height": "压缩图像最大尺寸高度", "Image Max Compression Size width": "压缩图像最大尺寸宽度", "Image Prompt Generation": "图像提示词生成", "Image Prompt Generation Prompt": "用于生成图像提示词的提示词", "Image Settings": "图像设置", "Images": "图像", "Import": "导入", "Import Chats": "导入对话记录", "Import Config from JSON File": "从 JSON 文件中导入配置信息", "Import From Link": "从链接导入", "Import Notes": "导入笔记", "Import Presets": "导入预设", "Import Prompt Suggestions": "导入提示词建议", "Import successful": "导入成功", "Important Update": "重要更新", "In order to force OCR, performing OCR must be enabled.": "开启“强制文字识别”选项需要先开启“文字识别“选项。", "Include": "包括", "Include `--api-auth` flag when running stable-diffusion-webui": "运行 stable-diffusion-webui 时包含 `--api-auth` 参数", "Include `--api` flag when running stable-diffusion-webui": "运行 stable-diffusion-webui 时包含 `--api` 参数", "Includes SharePoint": "包含 SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "影响算法对生成文本反馈的响应速度。较低的学习率会导致调整过程较慢，而较高的学习率将使算法反应更灵敏。", "Info": "信息", "Initials": "姓名缩写", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "注入整个内容作为上下文进行综合处理，适用于复杂查询", "Input": "输入", "Input commands": "输入命令", "Input Key (e.g. text, unet_name, steps)": "输入键名（例如：text, unet_name, steps）", "Input Variables": "插入变量", "Insert": "插入", "Insert Follow-Up Prompt to Input": "回填追问提示词到输入框", "Insert Prompt as Rich Text": "以富文本的形式回填提示词", "Insert Suggestion Prompt to Input": "回填推荐提示词到输入框", "Install from Github URL": "从 Github URL 安装", "Instant Auto-Send After Voice Transcription": "语音转录文字后即时自动发送", "Integration": "集成", "Integrations": "扩展功能", "Interface": "界面", "Invalid file content": "文件内容无效", "Invalid file format.": "文件格式无效", "Invalid JSON file": "JSON 文件格式无效", "Invalid JSON format for ComfyUI Workflow.": "ComfyUI 工作流中的 JSON 格式无效", "Invalid JSON format for Parameters": "额外参数中的 JSON 格式无效", "Invalid JSON format in Additional Config": "额外配置项中的 JSON 格式无效", "Invalid Tag": "无效标签", "is typing...": "输入中...", "Italic": "斜体", "January": "一月", "Jina API Key": "Jina API 密钥", "join our Discord for help.": "加入我们的 Discord 寻求帮助", "JSON": "JSON", "JSON Preview": "JSON 预览", "JSON Spec": "以 JSON 格式定义的接口规范", "July": "七月", "June": "六月", "Jupyter Auth": "Jupyter 身份验证", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT 过期时间", "JWT Token": "JWT 令牌", "Kagi Search API Key": "Kagi 搜索 API 密钥", "Keep Follow-Up Prompts in Chat": "在对话中保留追问提示词", "Keep in Sidebar": "保留在侧边栏", "Key": "密匙", "Key is required": "密匙是必填项。", "Keyboard shortcuts": "键盘快捷键", "Knowledge": "知识库", "Knowledge Access": "访问知识库", "Knowledge Base": "知识库", "Knowledge created successfully.": "知识库创建成功", "Knowledge deleted successfully.": "知识库删除成功", "Knowledge Description": "知识库描述", "Knowledge Name": "知识库名称", "Knowledge Public Sharing": "公开分享知识库", "Knowledge reset successfully.": "知识库重置成功", "Knowledge updated successfully": "知识库更新成功", "Kokoro.js (Browser)": "Kokoro.js（运行于用户浏览器）", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "标签", "Landing Page Mode": "默认主页样式", "Language": "语言", "Language Locales": "语言环境", "Last Active": "最后在线时间", "Last Modified": "最后修改时间", "Last reply": "最后回复", "LDAP": "LDAP", "LDAP server updated": "LDAP 服务器已更新", "Leaderboard": "排行榜", "Learn More": "了解更多", "Learn more about OpenAPI tool servers.": "进一步了解 OpenAPI 工具服务器", "Leave empty for no compression": "留空则不压缩", "Leave empty for unlimited": "留空则无限制", "Leave empty to include all models from \"{{url}}\" endpoint": "留空以包含来自 \"{{url}}\" 端点的所有模型", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "留空以包含来自 \"{{url}}/api/tags\" 端点的所有模型", "Leave empty to include all models from \"{{url}}/models\" endpoint": "留空以包含来自 \"{{url}}/models\" 端点的所有模型", "Leave empty to include all models or select specific models": "留空以包含所有模型或手动选择模型", "Leave empty to use the default prompt, or enter a custom prompt": "留空以使用默认提示词，或输入自定义提示词", "Leave model field empty to use the default model.": "模型字段留空以使用默认模型", "Legacy": "旧版", "lexical": "关键词", "License": "授权", "Lift List": "上移列表", "Light": "浅色", "Listening...": "正在倾听...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "大语言模型可能会犯错，请核实关键信息。", "Loader": "加载器", "Loading Kokoro.js...": "加载 Kokoro.js 中...", "Loading...": "加载中...", "Local": "本地", "Local Task Model": "本地任务模型", "Location access not allowed": "不允许访问位置信息", "Lost": "落败", "Low": "低", "LTR": "从左至右", "Made by Open WebUI Community": "由 Open WebUI 社区开发", "Make password visible in the user interface": "在用户界面中显示密码", "Make sure to enclose them with": "确保将它们包含在内", "Make sure to export a workflow.json file as API format from ComfyUI.": "确保从 ComfyUI 导出 API 格式的 workflow.json 文件。", "Male": "男性", "Manage": "管理", "Manage Direct Connections": "管理直接连接", "Manage Models": "管理模型", "Manage Ollama": "管理 <PERSON><PERSON>ma", "Manage Ollama API Connections": "管理 Ollama API 连接", "Manage OpenAI API Connections": "管理 OpenAI API 连接", "Manage Pipelines": "管理 Pipeline", "Manage Tool Servers": "管理工具服务器", "Manage your account information.": "管理您的账号信息。", "March": "三月", "Markdown": "<PERSON><PERSON>", "Markdown (Header)": "Markdown（标题）", "Max Speakers": "最大扬声器数量", "Max Upload Count": "最大上传数量", "Max Upload Size": "最大上传大小", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "最多可同时下载 3 个模型，请稍后重试。", "May": "五月", "MCP": "MCP", "MCP support is experimental and its specification changes often, which can lead to incompatibilities. OpenAPI specification support is directly maintained by the Open WebUI team, making it the more reliable option for compatibility.": "MCP 支持仍处于实验阶段，因其规范变化频繁，可能会出现不兼容的情况。而 OpenAPI 规范由 Open WebUI 团队维护，在兼容性方面更加可靠。", "Medium": "中", "Memories accessible by LLMs will be shown here.": "大语言模型可访问的记忆将在此显示", "Memory": "记忆", "Memory added successfully": "记忆添加成功", "Memory cleared successfully": "记忆清除成功", "Memory deleted successfully": "记忆删除成功", "Memory updated successfully": "记忆更新成功", "Merge Responses": "合并回复", "Merged Response": "合并的回复", "Message rating should be enabled to use this feature": "要使用此功能，需先启用回复评价功能", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "创建链接后发送的消息将不会被分享。通过该链接访问的用户可以查看对话记录。", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive（个人账户）", "Microsoft OneDrive (work/school)": "Microsoft OneDrive（工作或学校账户）", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "需要 Mistral OCR API 密钥", "Model": "模型", "Model '{{modelName}}' has been successfully downloaded.": "模型“{{modelName}}”已成功下载", "Model '{{modelTag}}' is already in queue for downloading.": "模型“{{modelTag}}”已在下载队列中", "Model {{modelId}} not found": "未找到模型 {{modelId}}", "Model {{modelName}} is not vision capable": "模型 {{modelName}} 不支持视觉能力", "Model {{name}} is now {{status}}": "模型 {{name}} 现在是 {{status}}", "Model {{name}} is now hidden": "模型 {{name}} 已隐藏", "Model {{name}} is now visible": "模型 {{name}} 已可见", "Model accepts file inputs": "模型支持文件输入", "Model accepts image inputs": "模型支持图像输入", "Model can execute code and perform calculations": "模型可执行代码并进行计算", "Model can generate images based on text prompts": "模型可根据文本提示生成图像", "Model can search the web for information": "模型可进行联网搜索", "Model created successfully!": "模型创建成功！", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "检测到模型名字为文件路径。需要提供模型简称才能继续执行更新。", "Model Filtering": "模型白名单", "Model ID": "模型 ID", "Model ID is required.": "模型 ID 是必填项。", "Model IDs": "模型 ID", "Model Name": "模型名称", "Model name already exists, please choose a different one": "模型名称已存在，请使用不同的名称", "Model Name is required.": "模型名称是必填项。", "Model not selected": "未选择模型", "Model Params": "模型参数", "Model Permissions": "模型权限", "Model unloaded successfully": "模型卸载成功", "Model updated successfully": "模型更新成功", "Model(s) do not support file upload": "模型不支持文件上传", "Modelfile Content": "模型文件内容", "Models": "模型", "Models Access": "访问模型列表", "Models configuration saved successfully": "模型配置保存成功", "Models imported successfully": "已成功导入模型配置", "Models Public Sharing": "模型公开共享", "Mojeek Search API Key": "Mojeek Search API 密钥", "More": "更多", "More Concise": "精炼表达", "More Options": "更多选项", "Move": "移动", "Name": "名称", "Name and ID are required, please fill them out": "名称和 ID 是必填项，请填写。", "Name your knowledge base": "为您的知识库命名", "Native": "原生", "New Button": "新按钮", "New Chat": "新对话", "New Folder": "创建分组", "New Function": "新函数", "New Knowledge": "", "New Model": "", "New Note": "新笔记", "New Password": "新密码", "New Prompt": "", "New Tool": "新工具", "new-channel": "新频道", "Next message": "下一条消息", "No authentication": "无身份验证", "No chats found": "未找到对话记录", "No chats found for this user.": "未找到此用户的对话记录", "No chats found.": "未找到对话记录", "No content": "没有内容", "No content found": "未发现内容", "No content found in file.": "文件中未找到内容", "No content to speak": "没有内容可朗读", "No conversation to save": "没有可保存的对话", "No distance available": "没有可用距离", "No feedbacks found": "暂无任何反馈", "No file selected": "未选中文件", "No functions found": "", "No groups with access, add a group to grant access": "没有权限组，请添加一个权限组以授予访问权限", "No HTML, CSS, or JavaScript content found.": "未找到 HTML、CSS 或 JavaScript 内容。", "No inference engine with management support found": "未找到支持管理的推理引擎", "No knowledge found": "未找到知识", "No memories to clear": "记忆为空，无须清理", "No model IDs": "没有模型 ID", "No models found": "未找到任何模型", "No models selected": "未选择任何模型", "No Notes": "没有笔记", "No notes found": "没有任何笔记", "No prompts found": "", "No results": "未找到结果", "No results found": "未找到结果", "No search query generated": "未生成搜索查询", "No source available": "没有可用引用来源", "No sources found": "未找到任何引用来源", "No suggestion prompts": "没有推荐提示词", "No tools found": "", "No users were found.": "未找到用户", "No valves": "没有配置项", "No valves to update": "没有需要更新的配置项", "Node Ids": "节点 ID", "None": "无", "Not factually correct": "与事实不符", "Not helpful": "没有任何帮助", "Not Registered": "未注册", "Note": "笔记", "Note deleted successfully": "笔记删除成功", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "注意：如果设置了最低分数，搜索结果只会返回分数大于或等于最低分数的文档。", "Notes": "笔记", "Notes Public Sharing": "公开分享笔记", "Notification Sound": "通知提示音", "Notification Webhook": "通知 Webhook", "Notifications": "桌面通知", "November": "十一月", "OAuth": "OAuth", "OAuth 2.1": "OAuth 2.1", "OAuth ID": "OAuth ID", "October": "十月", "Off": "关闭", "Okay, Let's Go!": "确认，开始使用！", "OLED Dark": "漆黑", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API 设置已更新", "Ollama Cloud API Key": "Ollama Cloud API 密钥", "Ollama Version": "Ollama 版本", "On": "开启", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "仅在启用“粘贴大文本为文件”功能时生效。", "Only active when the chat input is in focus and an LLM is generating a response.": "仅在对话输入框激活且大语言模型正在生成回复时生效。", "Only alphanumeric characters and hyphens are allowed": "只允许使用英文字母，数字 (0-9) 以及连字符 (-)", "Only alphanumeric characters and hyphens are allowed in the command string.": "命令字符串中只允许使用英文字母，数字 (0-9) 以及连字符 (-)。", "Only collections can be edited, create a new knowledge base to edit/add documents.": "只能编辑文件集，创建一个新的知识库来编辑/添加文件。", "Only markdown files are allowed": "仅允许使用 markdown 文件", "Only select users and groups with permission can access": "只有具有权限的用户和组才能访问", "Oops! Looks like the URL is invalid. Please double-check and try again.": "糟糕！此链接似乎为无效链接。请检查后重试。", "Oops! There are files still uploading. Please wait for the upload to complete.": "糟糕！仍有文件正在上传。请等待上传完成。", "Oops! There was an error in the previous response.": "糟糕！之前的回复中出现了错误。", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "糟糕！您正在使用不受支持的方法（仅运行前端服务）。请通过后端服务提供 WebUI。", "Open file": "打开文件", "Open in full screen": "全屏打开", "Open link": "打开链接", "Open modal to configure connection": "打开外部连接配置弹窗", "Open Modal To Manage Floating Quick Actions": "管理快捷操作浮窗", "Open Modal To Manage Image Compression": "打开图片压缩配置窗口", "Open new chat": "打开新对话", "Open Sidebar": "展开侧边栏", "Open User Profile Menu": "打开个人资料菜单", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI 可使用任何 OpenAPI 服务器提供的工具。", "Open WebUI uses faster-whisper internally.": "Open WebUI 使用内置 faster-whisper", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI 使用 SpeechT5 和 CMU Arctic speaker embedding。", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "当前 Open WebUI 版本 (v{{OPEN_WEBUI_VERSION}}) 低于所需的版本 (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API 配置", "OpenAI API Key is required.": "OpenAI API 密钥是必填项。", "OpenAI API settings updated": "OpenAI API 设置已更新", "OpenAI URL/Key required.": "OpenAI URL/Key 是必填项。", "OpenAPI": "OpenAPI", "OpenAPI Spec": "OpenAPI 接口规范", "openapi.json URL or Path": "openapi.json URL 或路径", "Optional": "可选", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "在本地运行视觉语言模型以获取图片内容描述。需要填写托管在 Hugging Face 上的模型，不可与 picture_description_api 同时使用。", "or": "或", "Ordered List": "有序列表", "Organize your users": "组织用户", "Other": "其他", "OUTPUT": "输出", "Output format": "输出格式", "Output Format": "输出格式", "Overview": "概述", "page": "页", "Paginate": "分页", "Parameters": "参数", "Password": "密码", "Passwords do not match.": "两次输入的密码不一致。", "Paste Large Text as File": "粘贴大文本为文件", "PDF Backend": "PDF 解析器后端", "PDF document (.pdf)": "PDF 文档 (.pdf)", "PDF Extract Images (OCR)": "PDF 图像提取（使用文字识别）", "pending": "待激活", "Pending": "待激活", "Pending User Overlay Content": "待激活用户界面内容", "Pending User Overlay Title": "待激活用户界面标题", "Perform OCR": "文字识别", "Permission denied when accessing media devices": "申请媒体设备权限被拒绝", "Permission denied when accessing microphone": "申请麦克风权限被拒绝", "Permission denied when accessing microphone: {{error}}": "申请麦克风权限被拒绝：{{error}}", "Permissions": "权限", "Perplexity API Key": "Perplexity API 密钥", "Perplexity Model": "Perplexity 模型", "Perplexity Search Context Usage": "Perplexity 搜索上下文用量", "Personalization": "个性化", "Picture Description API Config": "图片描述 API 配置", "Picture Description Local Config": "图片描述本地配置", "Picture Description Mode": "图片描述模式", "Pin": "置顶", "Pinned": "已置顶", "Pioneer insights": "洞悉未来", "Pipe": "<PERSON><PERSON>", "Pipeline": "处理管线", "Pipeline deleted successfully": "Pipeline 删除成功", "Pipeline downloaded successfully": "Pipeline 下载成功", "Pipelines": "Pipeline", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines 是具有任意代码执行风险的插件系统 —", "Pipelines Not Detected": "未检测到 Pipeline", "Pipelines Valves": "Pipeline 配置项", "Plain text (.md)": "纯文本文档（.md）", "Plain text (.txt)": "纯文本文档 (.txt)", "Playground": "AI 对话探索区", "Playwright Timeout (ms)": "Playwright 超时时间 (ms)", "Playwright WebSocket URL": "Playwright WebSocket URL", "Please carefully review the following warnings:": "请仔细阅读以下警告信息：", "Please do not close the settings page while loading the model.": "加载模型时请不要关闭设置页面", "Please enter a message or attach a file.": "请输入内容或添加文件。", "Please enter a prompt": "请输入提示词", "Please enter a valid ID": "请输入有效的 ID", "Please enter a valid JSON spec": "请输入有效 JSON 格式的接口规范", "Please enter a valid path": "请输入有效路径", "Please enter a valid URL": "请输入有效的 URL", "Please enter a valid URL.": "请输入有效的 URL。", "Please fill in all fields.": "请填写所有字段。", "Please register the OAuth client": "请注册 OAuth 客户端", "Please save the connection to persist the OAuth client information and do not change the ID": "请保存连接以保留 OAuth 客户端信息，并确保不要更改 ID", "Please select a model first.": "请先选择模型", "Please select a model.": "请选择模型。", "Please select a reason": "请选择原因", "Please select a valid JSON file": "请选择合法的 JSON 文件", "Please wait until all files are uploaded.": "请等待所有文件上传完毕。", "Port": "端口", "Positive attitude": "态度积极", "Prefer not to say": "暂不透露", "Prefix ID": "模型 ID 前缀", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "在模型 ID 前添加前缀以避免与其它连接提供的模型冲突。留空则禁用此功能。", "Prevent file creation": "阻止文件创建", "Preview": "预览", "Previous 30 days": "过去 30 天", "Previous 7 days": "过去 7 天", "Previous message": "上一条消息", "Private": "私有", "Profile": "个人资料", "Prompt": "提示词", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "提示词（例如：给我讲一则罗马帝国的趣事）", "Prompt Autocompletion": "自动补全提示词", "Prompt Content": "提示词内容", "Prompt created successfully": "提示词创建成功", "Prompt suggestions": "提示词建议", "Prompt updated successfully": "提示词更新成功", "Prompts": "提示词", "Prompts Access": "访问提示词", "Prompts Public Sharing": "提示词公开共享", "Provider Type": "服务提供商类型", "Public": "公共", "Pull \"{{searchValue}}\" from Ollama.com": "从 Ollama.com 拉取“{{searchValue}}”", "Pull a model from Ollama.com": "从 Ollama.com 拉取一个模型", "Pull Model": "", "pypdfium2": "pypdfium2", "Query Generation Prompt": "查询生成提示词", "Querying": "查询中", "Quick Actions": "快捷操作", "RAG Template": "RAG 提示词模板", "Rating": "评价", "Re-rank models by topic similarity": "根据主题相似性对模型重新排名", "Read": "只读", "Read Aloud": "朗读", "Read more →": "了解更多 →", "Reason": "推理", "Reasoning Effort": "推理努力 (<PERSON><PERSON> Effort)", "Reasoning Tags": "推理过程标签", "Record": "录制", "Record voice": "录音", "Redirecting you to Open WebUI Community": "正在将您重定向到 Open WebUI 社区", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "降低生成无意义内容的概率。较高的值（如 100）将生成更多样化的回答，而较低的值（如 10）则更加保守。", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "使用\"User\" (用户) 来指代自己（例如：“User 正在学习西班牙语”）", "Reference Chats": "引用其他对话", "Refused when it shouldn't have": "拒绝了我的要求", "Regenerate": "重新生成", "Regenerate Menu": "显示重新生成选项菜单", "Register Again": "重新注册", "Register Client": "注册客户端", "Registered": "已注册", "Registration failed": "注册失败", "Registration successful": "注册成功", "Reindex": "重建索引", "Reindex Knowledge Base Vectors": "重建知识库向量索引", "Release Notes": "更新日志", "Releases": "发行版", "Relevance": "相关性", "Relevance Threshold": "相关性阈值", "Remember Dismissal": "记住关闭状态", "Remove": "移除", "Remove {{MODELID}} from list.": "从列表中移除 {{MODELID}}", "Remove file": "移除文件", "Remove File": "移除文件", "Remove image": "移除图像", "Remove Model": "移除模型", "Remove this tag from list": "从列表中移除此标签", "Rename": "重命名", "Reorder Models": "重新排序模型", "Reply": "回复", "Reply in Thread": "回复主题", "Reply to thread...": "回复主题...", "Replying to {{NAME}}": "回复 {{NAME}}", "required": "必填", "Reranking Engine": "重新排名引擎", "Reranking Model": "重新排名模型", "Reset": "重置", "Reset All Models": "重置所有模型", "Reset Image": "重置图片", "Reset Upload Directory": "重置上传目录", "Reset Vector Storage/Knowledge": "重置向量存储/知识", "Reset view": "重置视图", "Response": "回答", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "无法启用生成结果通知。请检查浏览器设置，并授予必要的访问权限。", "Response splitting": "拆分回复", "Response Watermark": "复制时添加水印", "Result": "结果", "RESULT": "结果", "Retrieval": "检索", "Retrieval Query Generation": "检索查询生成", "Retrieved {{count}} sources": "检索到 {{count}} 个引用来源", "Retrieved {{count}} sources_other": "检索到 {{count}} 个引用来源", "Retrieved 1 source": "检索到 1 个引用来源", "Rich Text Input for Chat": "富文本对话框", "RK": "排名", "Role": "角色", "Rosé Pine": "玫瑰松木", "Rosé Pine Dawn": "玫瑰松木·晨曦", "RTL": "从右至左", "Run": "运行", "Running": "运行中", "Running...": "运行中...", "Save": "保存", "Save & Create": "保存并创建", "Save & Update": "保存并更新", "Save As Copy": "另存为副本", "Save Chat": "保存对话", "Save Tag": "保存标签", "Saved": "已保存", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "我们不再支持将对话记录直接保存到浏览器的存储空间。请点击下面的按钮下载并删除您的对话记录。别担心，您可以轻松将对话记录重新导入到后台。", "Scroll On Branch Change": "切换对话分支时滚动到最新回答", "Search": "搜索", "Search a model": "搜索模型", "Search all emojis": "搜索 Emoji", "Search Base": "搜索库", "Search Chats": "搜索对话", "Search Collection": "搜索内容", "Search Filters": "搜索过滤器", "search for archived chats": "搜索已归档的对话", "search for folders": "搜索分组", "search for pinned chats": "搜索已置顶的对话", "search for shared chats": "搜索已共享的对话", "search for tags": "搜索标签", "Search Functions": "搜索函数", "Search In Models": "搜索模型", "Search Knowledge": "搜索知识", "Search Models": "搜索模型", "Search Notes": "搜索笔记", "Search options": "搜索选项", "Search Prompts": "搜索提示词", "Search Result Count": "搜索结果数量", "Search the internet": "联网搜索", "Search Tools": "搜索工具", "SearchApi API Key": "SearchApi API 密钥", "SearchApi Engine": "SearchApi 引擎", "Searched {{count}} sites": "已搜索 {{count}} 个网站", "Searching": "搜索中", "Searching \"{{searchQuery}}\"": "搜索 \"{{searchQuery}}\" 中", "Searching Knowledge for \"{{searchQuery}}\"": "检索有关 \"{{searchQuery}}\" 的知识中", "Searching the web": "正在搜索网络...", "Searxng Query URL": "Searxng 查询 URL", "See readme.md for instructions": "查看 readme.md 以获取说明", "See what's new": "查阅最新更新内容", "Seed": "种子 (Seed)", "Select": "选择", "Select a base model": "选择一个基础模型", "Select a base model (e.g. llama3, gpt-4o)": "选择一个基础模型（例如：llama3, gpt-4o）", "Select a conversation to preview": "选择对话进行预览", "Select a engine": "选择搜索引擎", "Select a function": "选择函数", "Select a group": "选择权限组", "Select a language": "选择语言", "Select a mode": "选择模式", "Select a model": "选择模型", "Select a model (optional)": "选择模型（可选）", "Select a pipeline": "选择 Pipeline", "Select a pipeline url": "选择 Pipeline URL", "Select a reranking model engine": "选择重排序模型引擎", "Select a role": "选择角色", "Select a theme": "选择主题", "Select a tool": "选择工具", "Select a voice": "选择声音", "Select an auth method": "选择身份验证方式", "Select an embedding model engine": "选择嵌入模型引擎", "Select an engine": "选择引擎", "Select an Ollama instance": "选择 Ollama 实例", "Select an output format": "选择输出格式", "Select dtype": "选择数据类型（Dtype）", "Select Engine": "选择引擎", "Select how to split message text for TTS requests": "选择消息文本拆分方法，用于 TTS 请求。", "Select Knowledge": "选择知识", "Select only one model to call": "只允许选择一个模型进行语音通话", "Select view": "", "Selected model(s) do not support image inputs": "所选择的模型不支持处理图像", "semantic": "语义", "Send": "发送", "Send a Message": "输入消息", "Send message": "发送消息", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "在请求中发送 `stream_options: { include_usage: true }`。启用此设置后，支持的提供商将在响应中返回 Token 用量信息。", "September": "九月", "SerpApi API Key": "SerpApi API 密钥", "SerpApi Engine": "SerpApi 引擎", "Serper API Key": "Serper API 密钥", "Serply API Key": "Serply API 密钥", "Serpstack API Key": "Serpstack API 密钥", "Server connection verified": "已验证服务器连接", "Session": "用户会话（Session）", "Set as default": "设为默认", "Set CFG Scale": "设置 CFG Scale", "Set Default Model": "设置默认模型", "Set embedding model": "设置嵌入模型", "Set embedding model (e.g. {{model}})": "设置嵌入模型（例如：{{model}}）", "Set Image Size": "设置图片分辨率", "Set reranking model (e.g. {{model}})": "设置重排序模型（例如：{{model}}）", "Set Sampler": "设置 Sampler", "Set Scheduler": "设置 Scheduler", "Set Steps": "设置步骤", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "设置将加载到 GPU 的层数。增加此值可以显著提高对 GPU 加速优化的模型性能，但也可能占用更多 GPU 资源，增加功耗。", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "设置用于计算的工作线程数量。该选项可控制并发处理传入请求的线程数量。增加该值可以提高高并发工作负载下的性能，但也可能消耗更多的 CPU 资源。", "Set Voice": "设置音色", "Set whisper model": "设置 whisper 模型", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "对至少出现过一次的标记设置固定偏置值。较高的值（例如 1.5）表示严厉惩罚重复，而较低的值（例如 0.9）则表示相对宽松。当值为 0 时，此功能将被禁用。", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "根据标记出现的次数，设置缩放偏置值惩罚重复。较高的值（例如 1.5）表示严厉惩罚重复，而较低的值（例如 0.9）则表示相对宽松。当值为 0 时，此功能将被禁用。", "Sets how far back for the model to look back to prevent repetition.": "设置模型回溯的范围，以防止重复。", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "设置用于内容生成的随机数种子。将其设置为特定数字可使模型针对同一提示词生成相同的文本。", "Sets the size of the context window used to generate the next token.": "设置用于生成下一个 Token 的上下文窗口的大小。", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "设置要使用的停止序列。在该模式下，大语言模型将停止生成文本并返回。可以通过在模型文件中指定多个单独的停止参数来设置多个停止模式。", "Settings": "设置", "Settings saved successfully!": "设置已成功保存！", "Share": "分享", "Share Chat": "分享对话", "Share to Open WebUI Community": "分享到 Open WebUI 社区", "Share your background and interests": "分享您的经历和兴趣爱好", "Shared with you": "", "Sharing Permissions": "共享权限", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "带星号 (*) 的快捷键受场景限制，仅在特定条件下生效。", "Show": "显示", "Show \"What's New\" modal on login": "版本更新后首次登录时显示“新功能介绍”弹窗", "Show Admin Details in Account Pending Overlay": "在待激活用户的界面中显示管理员邮箱等详细信息", "Show Formatting Toolbar": "显示文本格式工具栏", "Show image preview": "显示图像预览", "Show Model": "显示模型", "Show shortcuts": "显示快捷方式", "Show your support!": "表达您的支持！", "Showcased creativity": "很有创意", "Sign in": "登录", "Sign in to {{WEBUI_NAME}}": "登录 {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "使用 LDAP 登录 {{WEBUI_NAME}}", "Sign Out": "登出", "Sign up": "注册", "Sign up to {{WEBUI_NAME}}": "注册 {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "借助大语言模型（LLM）提升表格、表单、行内数学公式及版面检测的准确性，但会增加响应时间。默认值：False。", "Signing in to {{WEBUI_NAME}}": "正在登录 {{WEBUI_NAME}}", "Sink List": "下移列表", "sk-1234": "sk-1234", "Skip Cache": "跳过缓存", "Skip the cache and re-run the inference. Defaults to False.": "跳过缓存并重新执行推理。默认为关闭", "Something went wrong :/": "发生错误", "Sonar": "Sonar", "Sonar Deep Research": "Sonar Deep Research", "Sonar Pro": "Sonar Pro", "Sonar Reasoning": "Sonar Reasoning", "Sonar Reasoning Pro": "Sonar Reasoning Pro", "Sougou Search API sID": "搜狗搜索 API Secret ID", "Sougou Search API SK": "搜狗搜索 API Secret 密钥", "Source": "来源", "Speech Playback Speed": "语音播放速度", "Speech recognition error: {{error}}": "语音识别错误：{{error}}", "Speech-to-Text": "语音转文本", "Speech-to-Text Engine": "语音转文本引擎", "standard": "标准", "Start of the channel": "频道起点", "Start Tag": "起始标签", "Status Updates": "显示实时回答状态", "STDOUT/STDERR": "标准输出/标准错误", "Stop": "停止", "Stop Generating": "停止生成", "Stop Sequence": "停止序列 (Stop Sequence)", "Stream Chat Response": "流式对话响应 (Stream Chat Response)", "Stream Delta Chunk Size": "流式增量输出的分块大小（Stream Delta Chunk Size）", "Streamable HTTP": "流式 HTTP", "Strikethrough": "删除线", "Strip Existing OCR": "清除现有文字识别内容", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "清除 PDF 中现有的文字识别内容并重新识别文字。若启用“强制文字识别”则此设置无效。默认为关闭", "STT Model": "语音转文本模型", "STT Settings": "语音转文本设置", "Stylized PDF Export": "美化 PDF 导出", "Subtitle (e.g. about the Roman Empire)": "副标题（例如：聊聊罗马帝国）", "Success": "成功", "Successfully imported {{userCount}} users.": "成功导入 {{userCount}} 个用户。", "Successfully updated.": "成功更新", "Suggest a change": "提出修改建议", "Suggested": "建议", "Support": "支持", "Support this plugin:": "支持此插件", "Supported MIME Types": "支持的 MIME 类型", "Sync directory": "同步目录", "System": "系统", "System Instructions": "系统指令", "System Prompt": "系统提示词", "Table Mode": "表格模式", "Tag": "", "Tags": "标签", "Tags Generation": "标签生成", "Tags Generation Prompt": "标签生成提示词", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "无尾采样用于减少输出中出现概率较小的 Token 的影响。较高的值（例如 2.0）将进一步减少影响，而值 1.0 则禁用此设置。", "Talk to model": "与模型交谈", "Tap to interrupt": "点击以中断", "Task List": "任务列表", "Task Model": "任务模型", "Tasks": "任务", "Tavily API Key": "Tavily API 密钥", "Tavily Extract Depth": "Tavily 提取深度", "Tell us more:": "请告诉我们更多细节", "Temperature": "温度 (Temperature)", "Temporary Chat": "临时对话", "Temporary Chat by Default": "默认使用临时对话", "Text Splitter": "文本切分器", "Text-to-Speech": "文本转语音", "Text-to-Speech Engine": "文本转语音引擎", "Thanks for your feedback!": "感谢您的反馈！", "The Application Account DN you bind with for search": "您所绑定用于搜索的 Application Account DN", "The base to search for users": "搜索用户的 Base", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "批处理大小决定了一次可以处理多少个文本请求。更高的批处理大小可以提高模型的性能和速度，但也需要更多内存。", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "本插件的开发者是社区中充满热情的志愿者。如果此插件有帮助到您，请考虑为开发贡献一份力量。", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "排行榜基于 Elo 评级系统并实时更新。", "The format to return a response in. Format can be json or a JSON schema.": "响应返回格式。可为 json 或 JSON schema。", "The height in pixels to compress images to. Leave empty for no compression.": "图片压缩高度（像素）。留空则不压缩。", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "输入音频的语言。以 ISO-639-1 语言编码格式（例如：en）指定输入语言可提高准确性和响应速度。留空则自动检测语言。", "The LDAP attribute that maps to the mail that users use to sign in.": "映射到用户登录时使用的邮箱的 LDAP 属性。", "The LDAP attribute that maps to the username that users use to sign in.": "映射到用户登录时使用的用户名的 LDAP 属性。", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "排行榜目前处于 Beta 测试阶段，我们可能会在完善算法后调整评分计算方法。", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "最大文件大小 (MB)。如果文件大小超过此限制，则无法上传该文件。", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "在单次对话中可以使用的最大文件数。如果文件数超过此限制，则文件不会上传。", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "文本输出格式。可选 'json', 'markdown' 或 'html'。默认为 'markdown'。", "The passwords you entered don't quite match. Please double-check and try again.": "两次输入的密码不一致。请检查并再次尝试。", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "分值应介于 0.0 (0%) 和 1.0 (100%) 之间。", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "模型在流式输出时每次发送的增量文本块大小。数值越大，模型每次返回的文字会更多。", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "模型的温度。增加温度将使模型的回答更有创意。", "The Weight of BM25 Hybrid Search. 0 more semantic, 1 more lexical. Default 0.5": "BM25 混合检索权重（输入接近 0 的数字会倾向于全语义搜索，反之输入接近 1 的数字会倾向于关键词搜索，默认为 0.5）", "The width in pixels to compress images to. Leave empty for no compression.": "图片压缩宽度（像素）。留空则不压缩。", "Theme": "主题", "Thinking...": "正在思考...", "This action cannot be undone. Do you wish to continue?": "此操作无法撤销。您确认要继续吗？", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "欢迎来到 {{channelName}}，本频道创建于 {{createdAt}}。", "This chat won't appear in history and your messages will not be saved.": "此对话将不会出现在历史记录中，且您的消息不会被保存", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "这将确保您宝贵的对话数据被安全地保存到后台数据库中。谢谢！", "This feature is experimental and may be modified or discontinued without notice.": "此功能为实验性功能，可能会在未经通知的情况下修改或停用。", "This is a default user permission and will remain enabled.": "此权限已在默认用户配置中启用，当前会始终生效。", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "这是一项实验性功能，可能无法按预期运行，也可能会随时发生变化。", "This model is not publicly available. Please select another model.": "此模型未公开。请选择其他模型", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "此选项用于控制模型在收到请求后，保持常驻内存的时长（默认：5 分钟）", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "此选项控制刷新上下文时保留多少 Token。例如，如果设置为 2，则将保留对话上下文的最后 2 个 Token。保留上下文有助于保持对话的连续性，但可能会降低响应新主题的能力。", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "此选项用于启用或禁用 Ollama 的推理功能，该功能允许模型在生成响应前进行思考。启用后，模型需要花些时间处理对话上下文，从而生成更缜密的回复。", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "此项用于设置模型在其响应中可以生成的最大 Token 数。增加此限制可让模型输出更多内容，但也可能增加生成无用或不相关内容的可能性。", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "此选项将会删除文件集中所有文件，并用新上传的文件替换。", "This response was generated by \"{{model}}\"": "此回复由 \"{{model}}\" 生成", "This will delete": "这将删除", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "这将删除<strong>{{NAME}}</strong>及其<strong>所有内容</strong>。", "This will delete all models including custom models": "这将删除所有模型，包括自定义模型", "This will delete all models including custom models and cannot be undone.": "这将删除所有模型，包括自定义模型，且无法撤销。", "This will reset the knowledge base and sync all files. Do you wish to continue?": "这将重置知识库并同步所有文件。确认继续？", "Thorough explanation": "解释详尽", "Thought for {{DURATION}}": "思考用时 {{DURATION}}", "Thought for {{DURATION}} seconds": "思考用时 {{DURATION}} 秒", "Thought for less than a second": "思考用时小于 1 秒", "Thread": "主题", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "请输入 Tika 服务器 URL", "Tiktoken": "Tiktoken", "Title": "标题", "Title (e.g. Tell me a fun fact)": "标题（例如：给我讲一个有趣的冷知识）", "Title Auto-Generation": "自动生成标题", "Title cannot be an empty string.": "标题不能为空", "Title Generation": "标题生成", "Title Generation Prompt": "用于自动生成标题的提示词", "TLS": "TLS", "To access the available model names for downloading,": "访问可下载的模型名称，", "To access the GGUF models available for downloading,": "访问可下载的 GGUF 模型，", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "请联系管理员获取访问权限。管理员可以在后台管理面板中管理用户状态。", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "如需在此处附加知识库，请先将其添加到工作空间中的“知识库”中", "To learn more about available endpoints, visit our documentation.": "如需了解更多关于可用端点的信息，请访问我们的文档", "To learn more about powerful prompt variables, click here": "如需了解更多关于强大的提示词变量的信息，请点击此处", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "为了保护您的隐私，只有评分、模型 ID、标签和元数据会从您的反馈中分享——您的对话记录将保持私密，不会被包含在内。", "To select toolkits here, add them to the \"Tools\" workspace first.": "如需在这里选择工具包，请先将其添加到工作空间中的“工具”", "Toast notifications for new updates": "检测到新版本时显示更新通知", "Today": "今天", "Today at {{LOCALIZED_TIME}}": "今天 {{LOCALIZED_TIME}}", "Toggle search": "切换搜索", "Toggle settings": "切换设置", "Toggle sidebar": "切换侧边栏", "Toggle whether current connection is active.": "切换当前连接的启用状态", "Token": "Token", "Too verbose": "过于冗长", "Tool created successfully": "工具创建成功", "Tool deleted successfully": "工具删除成功", "Tool Description": "工具描述", "Tool ID": "工具 ID", "Tool imported successfully": "工具导入成功", "Tool Name": "工具名称", "Tool Servers": "工具服务器", "Tool updated successfully": "工具更新成功", "Tools": "工具", "Tools Access": "访问工具", "Tools are a function calling system with arbitrary code execution": "工具是一个支持任意代码执行的函数调用系统", "Tools Function Calling Prompt": "工具函数调用提示词", "Tools have a function calling system that allows arbitrary code execution.": "注意：工具有权执行任意代码", "Tools Public Sharing": "工具公开共享", "Top K": "Top K", "Top K Reranker": "Top K Reranker", "Transformers": "Transformers", "Trouble accessing Ollama?": "访问 Ollama 时遇到问题？", "Trust Proxy Environment": "信任代理环境", "Try adjusting your search or filter to find what you are looking for.": "", "Try Again": "重新生成", "TTS Model": "文本转语音模型", "TTS Settings": "文本转语音设置", "TTS Voice": "文本转语音音色", "Type": "类型", "Type here...": "请输入内容...", "Type Hugging Face Resolve (Download) URL": "输入 Hugging Face 解析（下载）URL", "Uh-oh! There was an issue with the response.": "啊哦！回复有问题", "UI": "界面", "Unarchive All": "取消所有存档", "Unarchive All Archived Chats": "取消所有已存档的对话", "Unarchive Chat": "取消存档当前对话", "Underline": "下划线", "Unknown": "未知", "Unknown User": "未知用户", "Unloads {{FROM_NOW}}": "{{FROM_NOW}} 后卸载", "Unlock mysteries": "解码未知", "Unpin": "取消置顶", "Unravel secrets": "冲破奥秘", "Unsupported file type.": "不支持的文件类型", "Untagged": "无标签", "Untitled": "无标题", "Update": "更新", "Update and Copy Link": "更新和复制链接", "Update for the latest features and improvements.": "更新以获取最新功能与优化", "Update password": "更新密码", "Updated": "已更新", "Updated at": "更新于", "Updated At": "更新于", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "升级到授权套餐以获得增强功能，包括自定义主题、品牌推广以及专属支持", "Upload": "上传", "Upload a GGUF model": "上传一个 GGUF 模型", "Upload Audio": "上传音频", "Upload directory": "上传目录", "Upload files": "上传文件", "Upload Files": "上传文件", "Upload Model": "", "Upload Pipeline": "上传 Pipeline", "Upload Progress": "上传进度", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "上传进度：{{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)", "URL": "URL", "URL is required": "URL 是必填项。", "URL Mode": "URL 模式", "Usage": "用量", "Use '#' in the prompt input to load and include your knowledge.": "在输入框中输入 '#' 号可加载您需要的知识库内容", "Use groups to group your users and assign permissions.": "使用权限组设置用户分组并分配权限", "Use LLM": "使用大语言模型（LLM）", "Use no proxy to fetch page contents.": "不使用代理获取页面内容", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "使用由 http_proxy 和 https_proxy 环境变量指定的代理获取页面内容", "user": "用户", "User": "用户", "User Groups": "用户组", "User location successfully retrieved.": "成功检索到用户位置", "User menu": "用户菜单", "User Webhooks": "用户 Webhook", "Username": "用户名", "Users": "用户", "Uses DefaultAzureCredential to authenticate": "使用 DefaultAzureCredential 进行身份验证", "Uses OAuth 2.1 Dynamic Client Registration": "使用 OAuth 2.1 的动态客户端注册机制", "Using Entire Document": "使用完整文档", "Using Focused Retrieval": "使用聚焦检索", "Using the default arena model with all models. Click the plus button to add custom models.": "竞技场模型默认使用所有模型。点击上方的“+”按钮以添加自定义模型", "Valid time units:": "有效时间单位：", "Validate certificate": "验证证书合法性", "Valves": "配置项", "Valves updated": "配置项已更新", "Valves updated successfully": "配置项更新成功", "variable": "变量", "Verify Connection": "验证连接", "Verify SSL Certificate": "验证 SSL 证书", "Version": "版本", "Version {{selectedVersion}} of {{totalVersions}}": "版本 {{selectedVersion}}/{{totalVersions}}", "View Replies": "查看回复", "View Result from **{{NAME}}**": "查看来自 **{{NAME}}** 的结果", "Visibility": "可见性", "Vision": "视觉", "vlm": "视觉语言模型（VLM）", "Voice": "语音", "Voice Input": "语音输入", "Voice mode": "语音模式", "Warning": "警告", "Warning:": "警告：", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "警告：启用此功能将允许用户在服务器上上传任意代码", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "警告：如果您修改了嵌入模型，则需要重新导入所有文档", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "警告：启用 Jupyter 执行将允许运行任意代码，存在严重安全风险——务必谨慎操作", "Web": "网页", "Web API": "网页 API", "Web Loader Engine": "网页加载引擎", "Web Search": "联网搜索", "Web Search Engine": "联网搜索引擎", "Web Search in Chat": "在对话时进行联网搜索", "Web Search Query Generation": "联网搜索关键词生成", "Webhook URL": "Webhook URL", "Webpage URL": "网页 URL", "WebUI Settings": "WebUI 设置", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "WebUI 将向 \"{{url}}\" 发出请求", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI 将向 \"{{url}}/api/chat\" 发出请求", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI 将向 \"{{url}}/chat/completions\" 发出请求", "What are you trying to achieve?": "您想要达到什么目标？", "What are you working on?": "您在忙于什么？", "What's New in": "最近更新内容于", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "启用后，模型将实时回复每条对话信息，在用户发送信息后立即生成回复。这种模式适用于即时对话应用，但在性能较低的设备上可能会影响响应速度", "wherever you are": "纵使身在远方，亦与世界同频", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "是否对输出内容进行分页。每页之间将用水平分隔线和页码隔开。默认为关闭", "Whisper (Local)": "<PERSON>hisper (本地)", "Why?": "为什么？", "Widescreen Mode": "宽屏模式", "Width": "宽度", "Won": "获胜", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "与 top-k 配合使用。较高的值（例如 0.95）将产生更加多样化的文本，而较低的值（例如 0.5）将产生更加聚焦和保守的文本。", "Workspace": "工作空间", "Workspace Permissions": "工作空间权限", "Write": "可写", "Write a prompt suggestion (e.g. Who are you?)": "写一个提示词建议（例如：你是谁？）", "Write a summary in 50 words that summarizes [topic or keyword].": "用 50 个字写一份总结 [主题或关键词]", "Write something...": "写点什么...", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "请在此填写模型的系统提示词\n例如：你是《超级马里奥兄弟》中的马里奥（Mario），扮演助理的角色。", "Yacy Instance URL": "YaCy 实例 URL", "Yacy Password": "YaCy 密码", "Yacy Username": "YaCy 用户名", "Yesterday": "昨天", "Yesterday at {{LOCALIZED_TIME}}": "昨天 {{LOCALIZED_TIME}}", "You": "你", "You are currently using a trial license. Please contact support to upgrade your license.": "当前为试用许可证，请联系支持人员升级许可证。", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "每次对话最多仅能附上 {{maxCount}} 个文件。", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "点击下方“管理”按钮，您可以添加记忆，定制与大语言模型之间的互动，使其提供更加到位的帮助，更符合您的需求。", "You cannot upload an empty file.": "请勿上传空文件。", "You do not have permission to send messages in this channel.": "您没有权限在当前频道发送消息。", "You do not have permission to send messages in this thread.": "您没有权限在当前主题中发送消息。", "You do not have permission to upload files.": "您没有上传文件的权限。", "You have no archived conversations.": "没有已归档的对话。", "You have shared this chat": "此对话已经分享过", "You're a helpful assistant.": "你是一个乐于助人的助手。", "You're now logged in.": "已登录。", "Your Account": "您的账号", "Your account status is currently pending activation.": "您的账号当前状态为待激活", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "您的全部捐款将直接给到插件开发者，Open WebUI 不会收取任何分成。但众筹平台可能会有服务费。", "YouTube": "YouTube", "Youtube Language": "Youtube 语言", "Youtube Proxy URL": "Youtube 代理 URL"}