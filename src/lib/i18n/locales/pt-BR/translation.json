{"-1 for no limit, or a positive integer for a specific limit": "-1 para nenhum limite ou um inteiro positivo para um limite específico", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ou '-1' para sem expiração.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(por exemplo, `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(por exemplo, `sh webui.sh --api`)", "(latest)": "(último)", "(leave blank for to use commercial endpoint)": "(deixe em branco para usar o endpoint comercial)", "[Last] dddd [at] h:mm A": "[Último] dddd [em] h:mm A", "[Today at] h:mm A": "[Hoje às] h:mm A", "[Yesterday at] h:mm A": "[Ontem às] h:mm A", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} Ferramentas disponíveis", "{{COUNT}} characters": "{{COUNT}} caracteres", "{{COUNT}} extracted lines": "{{COUNT}} l<PERSON><PERSON>ídas", "{{COUNT}} hidden lines": "{{COUNT}} linhas ocultas", "{{COUNT}} Replies": "{{COUNT}} Respostas", "{{COUNT}} Sources": "{{COUNT}} Origens", "{{COUNT}} words": "{{COUNT}} palavras", "{{LOCALIZED_DATE}} at {{LOCALIZED_TIME}}": "{{LOCALIZED_DATE}} às {{LOCALIZED_TIME}}", "{{model}} download has been canceled": "O download do {{model}} foi cancelado", "{{user}}'s Chats": "Chats de {{user}}", "{{webUIName}} Backend Required": "Backend {{webUIName}} necess<PERSON>rio", "*Prompt node ID(s) are required for image generation": "*Prompt node ID(s) são obrigatórios para gerar imagens", "1 Source": "1 Origem", "A new version (v{{LATEST_VERSION}}) is now available.": "Um nova versão (v{{LATEST_VERSION}}) está disponível.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Um modelo de tarefa é usado ao realizar tarefas como gerar títulos para chats e consultas de pesquisa na web", "a user": "um usuário", "About": "Sobre", "Accept autocomplete generation / Jump to prompt variable": "Aceitar geração de preenchimento automático / Ir para variável de prompt", "Access": "Ace<PERSON>", "Access Control": "Controle de Acesso", "Accessible to all users": "Acessível para todos os usuários", "Account": "Conta", "Account Activation Pending": "Ativação da Conta Pendente", "accurate": "preciso", "Accurate information": "Informações precisas", "Action": "Ação", "Action not found": "Ação não encontrada", "Action Required for Chat Log Storage": "Ação necessária para salvar o registro do chat", "Actions": "Ações", "Activate": "Ativar", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Ativar esse comando no chat digitando \"/{{COMMAND}}\"", "Active": "Ativo", "Active Users": "Usuários Ativos", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Adicione um ID de modelo", "Add a short description about what this model does": "Adicione uma descrição curta sobre o que este modelo faz", "Add a tag": "Adici<PERSON>r uma tag", "Add Arena Model": "Adicionar Modelo Arena", "Add Connection": "<PERSON><PERSON><PERSON><PERSON>", "Add Content": "<PERSON><PERSON><PERSON><PERSON>", "Add content here": "Ad<PERSON><PERSON><PERSON> con<PERSON><PERSON><PERSON> aqui", "Add Custom Parameter": "Adicionar parâmetro personalizado", "Add custom prompt": "Adicionar prompt personalizado", "Add Details": "<PERSON><PERSON><PERSON><PERSON>", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "Adicionar Grupo", "Add Memory": "<PERSON><PERSON><PERSON><PERSON>", "Add Model": "<PERSON><PERSON><PERSON><PERSON>", "Add Reaction": "Adicionar <PERSON>", "Add Tag": "Adicionar <PERSON>", "Add Tags": "Adicionar Tags", "Add text content": "Adicionar con<PERSON><PERSON><PERSON> de texto", "Add User": "<PERSON><PERSON><PERSON><PERSON>", "Add User Group": "Adicionar grupo de usuários", "Additional Config": "Configuração adicional", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "Opções de configuração adicionais para o marcador. Deve ser uma string JSON com pares chave-valor. Por exemplo, '{\"key\": \"value\"}'. As chaves suportadas incluem: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level", "Additional Parameters": "", "Adjusting these settings will apply changes universally to all users.": "Ajustar essas configurações aplicará mudanças para todos os usuários.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Painel do Admin", "Admin Settings": "Configurações do Admin", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Os administradores têm acesso a todas as ferramentas o tempo todo; os usuários precisam de ferramentas atribuídas, por modelo, no workspace.", "Advanced Parameters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AI": "IA", "All": "<PERSON><PERSON>", "All chats have been unarchived.": "To<PERSON> os chats foram desarquivados.", "All Documents": "Todos os Documentos", "All models deleted successfully": "Todos os modelos foram excluídos com sucesso", "Allow Call": "<PERSON><PERSON><PERSON>", "Allow Chat Controls": "Permit<PERSON>", "Allow Chat Delete": "Permit<PERSON>", "Allow Chat Deletion": "Permit<PERSON>", "Allow Chat Edit": "Permitir <PERSON>", "Allow Chat Export": "Permitir Exportação de Chat", "Allow Chat Params": "<PERSON><PERSON><PERSON>", "Allow Chat Share": "<PERSON><PERSON><PERSON>", "Allow Chat System Prompt": "<PERSON><PERSON><PERSON> Prompt do Sistema no Chat", "Allow Chat Valves": "<PERSON><PERSON><PERSON>", "Allow Continue Response": "<PERSON><PERSON><PERSON>", "Allow Delete Messages": "Permitir <PERSON> de Mensagens", "Allow File Upload": "<PERSON><PERSON><PERSON>", "Allow Multiple Models in Chat": "<PERSON><PERSON><PERSON> no <PERSON>", "Allow non-local voices": "<PERSON><PERSON><PERSON> vozes não locais", "Allow Rate Response": "<PERSON><PERSON><PERSON>", "Allow Regenerate Response": "<PERSON><PERSON><PERSON>", "Allow Speech to Text": "<PERSON><PERSON><PERSON> para <PERSON>", "Allow Temporary Chat": "<PERSON><PERSON><PERSON>", "Allow Text to Speech": "Permitir <PERSON>", "Allow User Location": "Permitir Localização do Usuário", "Allow Voice Interruption in Call": "Permitir Interrupção de Voz na Chamada", "Allowed Endpoints": "Endpoints Permitidos", "Allowed File Extensions": "Extensões de arquivo permitidas", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Extensões de arquivo permitidas para upload. Separe várias extensões com vírgulas. Deixe em branco para todos os tipos de arquivo.", "Already have an account?": "Já possui uma conta?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternativa ao top_p, visando garantir um equilíbrio entre qualidade e variedade. O parâmetro p representa a probabilidade mínima de um token ser considerado, em relação à probabilidade do token mais provável. Por exemplo, com p = 0,05 e o token mais provável tendo uma probabilidade de 0,9, logits com valor inferior a 0,045 são filtrados.", "Always": "Sempre", "Always Collapse Code Blocks": "Sempre recolher blocos de código", "Always Expand Details": "Sempre expandir detalhes", "Always Play Notification Sound": "Sempre reproduzir som de notificação", "Amazing": "Incrível", "an assistant": "um assistente", "An error occurred while fetching the explanation": "Ocorreu um erro ao buscar a explicação", "Analytics": "<PERSON><PERSON><PERSON><PERSON>", "Analyzed": "<PERSON><PERSON><PERSON>", "Analyzing...": "<PERSON><PERSON><PERSON><PERSON>...", "and": "e", "and {{COUNT}} more": "e mais {{COUNT}}", "and create a new shared link.": "e criar um novo link compartilhado.", "Android": "Android", "API": "API", "API Base URL": "URL Base da API", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "URL base da API para o serviço Datalab Marker. O padrão é: https://www.datalab.to/api/v1/marker", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "Detalhes da API para usar um modelo de linguagem de visão na descrição da imagem. Este parâmetro é mutuamente exclusivo com picture_description_local.", "API Key": "Chave API", "API Key created.": "Chave API criada.", "API Key Endpoint Restrictions": "Restrições de endpoint de chave de API", "API keys": "Chaves API", "API Version": "Vers<PERSON> da API", "API Version is required": "Versão da API é obrigatória", "Application DN": "DN da Aplicação", "Application DN Password": "Senha da aplicação DN", "applies to all users with the \"user\" role": "Aplicar para todos com permissão de \"usuário\"", "April": "Abril", "Archive": "<PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "<PERSON><PERSON><PERSON><PERSON>", "Archived Chats": "Chats Arquivados", "archived-chat-export": "exportação de chats arquivados", "Are you sure you want to clear all memories? This action cannot be undone.": "Tem certeza de que deseja apagar todas as mem<PERSON><PERSON><PERSON>? Esta ação não pode ser desfeita.", "Are you sure you want to delete this channel?": "Tem certeza de que deseja excluir este canal?", "Are you sure you want to delete this message?": "Tem certeza de que deseja excluir esta mensagem?", "Are you sure you want to unarchive all archived chats?": "Você tem certeza que deseja desarquivar todos os chats arquivados?", "Are you sure?": "Você tem certeza?", "Arena Models": "Arena de Modelos", "Artifacts": "Arte<PERSON><PERSON>", "Ask": "Pergun<PERSON>", "Ask a question": "Faça uma pergunta", "Assistant": "<PERSON><PERSON><PERSON>", "Attach file from knowledge": "Anexar arquivo da base de conhecimento", "Attach Knowledge": "Anexar Base de Conhecimento", "Attach Notes": "<PERSON><PERSON><PERSON>", "Attach Webpage": "Anexar Página Web", "Attention to detail": "Atenção aos detalhes", "Attribute for Mail": "Atributo para E-mail", "Attribute for Username": "Atribuir para nome de usuário", "Audio": "<PERSON><PERSON><PERSON>", "August": "Agosto", "Auth": "Aut.", "Authenticate": "Autenticar", "Authentication": "Autenticação", "Auto": "Auto", "Auto-Copy Response to Clipboard": "Cópia Automática da Resposta para a Área de Transferência", "Auto-playback response": "Resposta de reprodução automática", "Autocomplete Generation": "Geração de preenchimento automático", "Autocomplete Generation Input Max Length": "Comprimento máximo de entrada de geração de preenchimento automático", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "String de Autenticação da API AUTOMATIC1111", "AUTOMATIC1111 Base URL": "URL Base AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "URL Base AUTOMATIC1111 é necessária.", "Available list": "Lista disponível", "Available Tools": "Ferramentas disponíveis", "available users": "usuários disponíveis", "available!": "disponível!", "Away": "Ausente", "Awful": "<PERSON><PERSON><PERSON><PERSON>", "Azure AI Speech": "Fala de IA do Azure", "Azure OpenAI": "Azure OpenAI", "Azure Region": "Região Azure", "Back": "Voltar", "Bad Response": "<PERSON>sp<PERSON><PERSON>", "Banners": "Banners", "Base Model (From)": "Modelo Base (De)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "O cache da lista de modelos base acelera o acesso buscando modelos base somente na inicialização ou ao salvar as configurações — mais rá<PERSON>o, mas pode não mostrar alterações recentes no modelo base.", "Bearer": "", "before": "antes", "Being lazy": "<PERSON><PERSON>", "Beta": "Beta", "Bing Search V7 Endpoint": "Endpoint do Bing Search V7", "Bing Search V7 Subscription Key": "Chave de assinatura do Bing Search V7", "Bio": "Sobre você", "Birth Date": "Data de nascimento", "BM25 Weight": "Peso BM25", "Bocha Search API Key": "<PERSON>ve da <PERSON> de pesquisa Bocha", "Bold": "Negrito", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Aumentar ou penalizar tokens específicos para respostas restritas. Os valores de viés serão fixados entre -100 e 100 (inclusive). (Padrão: nenh<PERSON>)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Tanto o Docling OCR Engine quanto o(s) idioma(s) devem ser fornecidos ou ambos devem ser deixados em branco.", "Brave Search API Key": "Chave API do Brave Search", "Bullet List": "Lista com marcadores", "Button ID": "ID do botão", "Button Label": "Rótulo do botão", "Button Prompt": "Prompt do botão", "By {{name}}": "Por {{name}}", "Bypass Embedding and Retrieval": "Ignorar incorporação e recuperação", "Bypass Web Loader": "<PERSON><PERSON><PERSON> da <PERSON>", "Cache Base Model List": "Lista de modelos base de cache", "Calendar": "<PERSON><PERSON><PERSON><PERSON>", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "O recurso de chamada não é suportado ao usar o mecanismo Web STT", "Camera": "Câmera", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Capacidades", "Capture": "Capturar", "Capture Audio": "Capturar Audio", "Certificate Path": "Caminho do Certificado", "Change Password": "<PERSON><PERSON><PERSON>", "Channel": "Canal", "Channel deleted successfully": "Canal apagado com sucesso", "Channel Name": "Nome do canal", "Channel updated successfully": "Canal atualizado com sucesso", "Channels": "Canais", "Character": "<PERSON><PERSON>", "Character limit for autocomplete generation input": "Limite de caracteres para entrada de geração de preenchimento automático", "Chart new frontiers": "Trace novas fronteiras", "Chat": "Cha<PERSON>", "Chat Background Image": "Imagem de Fundo do Chat", "Chat Bubble UI": "<PERSON><PERSON> de <PERSON>ha <PERSON>", "Chat Controls": "<PERSON>es de Chat", "Chat Conversation": "Conversa do Chat", "Chat direction": "Direção do Chat", "Chat ID": "ID do Chat", "Chat moved successfully": "Chat movido com sucesso", "Chat Overview": "Visão Geral do Chat", "Chat Permissions": "Permissões de Chat", "Chat Tags Auto-Generation": "Tags de Chat Geradas Automaticamente", "Chats": "Chats", "Check Again": "Verificar Novamente", "Check for updates": "Verificar atualizações", "Checking for updates...": "Verificando atualizações...", "Choose a model before saving...": "Escolha um modelo antes de salvar...", "Chunk Overlap": "Sobreposição de Chunk", "Chunk Size": "Tamanho do <PERSON>k", "Ciphers": "Cifras", "Citation": "Citação", "Citations": "Citações", "Clear memory": "<PERSON><PERSON>", "Clear Memory": "<PERSON><PERSON>", "click here": "Clique aqui", "Click here for filter guides.": "Clique aqui para obter instruções de filtros.", "Click here for help.": "Clique aqui para obter ajuda.", "Click here to": "Clique aqui para", "Click here to download user import template file.": "Clique aqui para baixar o arquivo de modelo de importação de usuários.", "Click here to learn more about faster-whisper and see the available models.": "Clique aqui para aprender mais sobre Whisper e ver os modelos disponíveis.", "Click here to see available models.": "Clique aqui para ver os modelos disponíveis.", "Click here to select": "Clique aqui para enviar", "Click here to select a csv file.": "Clique aqui para enviar um arquivo csv.", "Click here to select a py file.": "Clique aqui para enviar um arquivo python.", "Click here to upload a workflow.json file.": "Clique aqui para enviar um arquivo workflow.json.", "click here.": "clique aqui.", "Click on the user role button to change a user's role.": "Clique no botão de função do usuário para alterar a função de um usuário.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Permissão de escrita na área de transferência negada. Verifique as configurações do seu navegador para conceder o acesso necessário.", "Clone": "Clonar", "Clone Chat": "<PERSON><PERSON><PERSON>", "Clone of {{TITLE}}": "<PERSON><PERSON> de {{TITLE}}", "Close": "<PERSON><PERSON><PERSON>", "Close Banner": "<PERSON><PERSON><PERSON>", "Close Configure Connection Modal": "Fechar a Modal de Configuração de Conexão", "Close modal": "<PERSON><PERSON><PERSON> modal", "Close settings modal": "<PERSON><PERSON>r configurações modal", "Close Sidebar": "<PERSON><PERSON><PERSON> barra lateral", "CMU ARCTIC speaker embedding name": "", "Code Block": "Bloco de código", "Code execution": "Execução de código", "Code Execution": "Execução de código", "Code Execution Engine": "Mecanismo de execução de código", "Code Execution Timeout": "Tempo limite de execução do código", "Code formatted successfully": "Código formatado com sucesso", "Code Interpreter": "Intérprete de código", "Code Interpreter Engine": "Motor de interpretação de código", "Code Interpreter Prompt Template": "Modelo de Prompt do Interpretador de Código", "Collapse": "<PERSON><PERSON><PERSON><PERSON>", "Collection": "Coleção", "Color": "Cor", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Chave de API do ComfyUI", "ComfyUI Base URL": "URL Base do ComfyUI", "ComfyUI Base URL is required.": "URL Base do ComfyUI é necessária.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Comma separated Node Ids (e.g. 1 or 1,2)": "IDs de Nodes separados por vírgula (por exemplo, 1 ou 1,2)", "Command": "Comand<PERSON>", "Comment": "<PERSON><PERSON><PERSON><PERSON>", "Completions": "<PERSON><PERSON>lus<PERSON><PERSON>", "Compress Images in Channels": "Comprimir imagens em canais", "Concurrent Requests": "Solicitações simultâneas", "Config imported successfully": "Configuração importada com sucesso", "Configure": "Configurar", "Confirm": "Confirmar", "Confirm Password": "Confirmar <PERSON>", "Confirm your action": "Confirme sua ação", "Confirm your new password": "Confirme sua nova senha", "Confirm Your Password": "Confirme sua senha", "Connect to your own OpenAI compatible API endpoints.": "Conecte-se aos seus próprios endpoints de API compatíveis com OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "Conecte-se aos seus próprios servidores de ferramentas externas compatíveis com OpenAPI.", "Connection failed": "Falha na conexão", "Connection successful": "Conexão bem-sucedida", "Connection Type": "Tipo de <PERSON>", "Connections": "Conexões", "Connections saved successfully": "Conexões salvas com sucesso", "Connections settings updated": "Configurações de conexão atualizadas", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Restringe o esforço de raciocínio para modelos de raciocínio. Aplicável somente a modelos de raciocínio de provedores específicos que suportam o esforço de raciocínio.", "Contact Admin for WebUI Access": "Contate o Administrador para verificar seu acesso", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction Engine": "Mecanismo de Extração de Conteúdo", "Continue Response": "<PERSON><PERSON><PERSON><PERSON>", "Continue with {{provider}}": "Continuar com {{provider}}", "Continue with Email": "Continuar com Email", "Continue with LDAP": "Continuar com LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Controlar como o texto do mensagem é dividido para solicitações TTS. 'Pontuação' dividida em frases, 'parágrafos' divide em parágrafos e 'não' mantém a mensagem como uma cadeia de caracteres.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Controla a repetição de sequências de tokens no texto gerado. Um valor mais alto (por exemplo, 1,5) penalizará as repetições com mais rigor, enquanto um valor mais baixo (por exemplo, 1,1) será mais tolerante. Em 1, ele está desabilitado.", "Controls": "Controles", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Controla o equilíbrio entre coerência e diversidade da saída. Um valor menor resultará em um texto mais focado e coerente.", "Conversation saved successfully": "Conversa salva com sucesso", "Copied": "Copiado", "Copied link to clipboard": "Link copiado para a área de transferência", "Copied shared chat URL to clipboard!": "URL do chat compartilhado copiada para a área de transferência!", "Copied to clipboard": "Copiado para a área de transferência", "Copy": "Copiar", "Copy Formatted Text": "Copiar texto formatado", "Copy last code block": "Copiar último bloco de código", "Copy last response": "Copiar última resposta", "Copy link": "Copiar link", "Copy Link": "Copiar Link", "Copy to clipboard": "Copiar para a área de transferência", "Copying to clipboard was successful!": "Cópia para a área de transferência bem-sucedida!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "O CORS deve ser configurado corretamente pelo provedor para permitir solicitações do Open WebUI.", "Create": "<PERSON><PERSON><PERSON>", "Create a knowledge base": "Criar uma Base de Conhecimento", "Create a model": "<PERSON><PERSON><PERSON> um <PERSON>", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "<PERSON><PERSON><PERSON>ta de Administrador", "Create Channel": "Criar Canal", "Create Folder": "<PERSON><PERSON><PERSON>", "Create Group": "Criar Grupo", "Create Knowledge": "Criar Base de Conhecimento", "Create Model": "", "Create new key": "Criar nova chave", "Create new secret key": "Criar nova chave secreta", "Create Note": "<PERSON><PERSON><PERSON>", "Create your first note by clicking on the plus button below.": "Crie sua primeira nota clicando no botão de adição abaixo.", "Created at": "C<PERSON><PERSON> em", "Created At": "Criado Em", "Created by": "<PERSON><PERSON><PERSON> por", "Created by you": "", "CSV Import": "Importação CSV", "Ctrl+Enter to Send": "Ctrl+Enter para enviar", "Current Model": "<PERSON><PERSON>", "Current Password": "<PERSON><PERSON>", "Custom": "Personalizado", "Custom description enabled": "Descrição personalizada habilitada", "Custom Parameter Name": "Nome do parâmetro personalizado", "Custom Parameter Value": "Valor do parâmetro personalizado", "Danger Zone": "Zona de perigo", "Dark": "Escuro", "Data Controls": "Controle de Dados", "Database": "Banco de Dados", "Datalab Marker API": "API do Marcador do Datalab", "Datalab Marker API Key required.": "Chave de API do Datalab Marker necessária.", "DD/MM/YYYY": "DD/MM/AAAA", "December": "Dezembro", "Deepgram": "", "Default": "Padrão", "Default (Open AI)": "<PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Padrão (SentenceTransformers)", "Default action buttons will be used.": "Botões de ação padrão serão usados.", "Default description enabled": "Descrição padrão habilitada", "Default Features": "Recurs<PERSON> pad<PERSON>", "Default Filters": "<PERSON><PERSON><PERSON>", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "O modo padrão funciona com uma gama mais ampla de modelos, chamando as ferramentas uma vez antes da execução. O modo nativo aproveita os recursos integrados de chamada de ferramentas do modelo, mas exige que o modelo suporte esse recurso inerentemente.", "Default Model": "<PERSON><PERSON>", "Default model updated": "<PERSON><PERSON> atualizado", "Default Models": "<PERSON><PERSON>", "Default permissions": "Permissões padr<PERSON>", "Default permissions updated successfully": "Permissões padrão atualizadas com sucesso", "Default Prompt Suggestions": "Sugestões de Prompt Padrão", "Default to 389 or 636 if TLS is enabled": "O padrão é 389 ou 636 se o TLS estiver habilitado", "Default to ALL": "Padrão para TODOS", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Use a recuperação segmentada como padrão para extração de conteúdo focado e relevante; isso é recomendado para a maioria dos casos.", "Default User Role": "Padrão para novos usuários", "Delete": "Excluir", "Delete a model": "Excluir um modelo", "Delete All Chats": "Excluir Todos os Chats", "Delete All Models": "Excluir Todos os Modelos", "Delete chat": "Excluir chat", "Delete Chat": "Excluir <PERSON>", "Delete chat?": "Excluir chat?", "Delete folder?": "Excluir pasta?", "Delete function?": "Excluir função?", "Delete Message": "Excluir mensagem", "Delete message?": "Excluir mensagem?", "Delete Model": "", "Delete note?": "Excluir nota?", "Delete prompt?": "Excluir prompt?", "delete this link": "Excluir este link", "Delete tool?": "Excluir ferramenta?", "Delete User": "Excluir <PERSON>", "Deleted {{deleteModelTag}}": "Excluído {{deleteModelTag}}", "Deleted {{name}}": "<PERSON>clu<PERSON><PERSON> {{name}}", "Deleted User": "<PERSON><PERSON><PERSON><PERSON>", "Deployment names are required for Azure OpenAI": "Nomes de implantação são necessários para o Azure OpenAI", "Describe Pictures in Documents": "Descreva imagens em documentos", "Describe your knowledge base and objectives": "Descreva sua base de conhecimento e objetivos", "Description": "Descrição", "Detect Artifacts Automatically": "Detectar artefatos automaticamente", "Dictate": "Ditar", "Didn't fully follow instructions": "Não seguiu completamente as instruções", "Direct": "Direto", "Direct Connections": "Conexões Diretas", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "As conexões diretas permitem que os usuários se conectem aos seus próprios terminais de API compatíveis com OpenAI.", "Direct Tool Servers": "Servidores de ferramentas diretas", "Directory selection was cancelled": "A seleção do diretório foi cancelada", "Disable Code Interpreter": "Desativar o interpretador de código", "Disable Image Extraction": "Desativar extração de imagem", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "Desabilite a extração de imagens do PDF. Se a opção Usar LLM estiver habilitada, as imagens serão legendadas automaticamente. O padrão é Falso.", "Disabled": "Desativado", "Discover a function": "Descubra uma função", "Discover a model": "Descubra um modelo", "Discover a prompt": "Descubra um prompt", "Discover a tool": "Descubra uma ferramenta", "Discover how to use Open WebUI and seek support from the community.": "Descubra como usar o Open WebUI e busque suporte da comunidade.", "Discover wonders": "Descobrir ma<PERSON>", "Discover, download, and explore custom functions": "Descubra, baixe e explore funções personalizadas", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, baixe e explore prompts personalizados", "Discover, download, and explore custom tools": "Descubra, baixe e explore ferramentas personalizadas", "Discover, download, and explore model presets": "Descubra, baixe e explore predefinições de modelos", "Display": "<PERSON><PERSON><PERSON>", "Display chat title in tab": "Exibir título do chat na aba", "Display Emoji in Call": "<PERSON><PERSON><PERSON>", "Display Multi-model Responses in Tabs": "<PERSON><PERSON><PERSON> respostas de vários modelos em guias", "Display the username instead of You in the Chat": "Exibir o nome de usuário em vez de Você no Chat", "Displays citations in the response": "Exibir citações na resposta", "Displays status updates (e.g., web search progress) in the response": "Exibe atualizações de status (por exemplo, progresso da pesquisa na web) na resposta", "Dive into knowledge": "Explorar base de conhecimento", "dlparse_v1": "", "dlparse_v2": "", "dlparse_v4": "", "Do not install functions from sources you do not fully trust.": "Não instale funções de origens nas quais você não confia totalmente.", "Do not install tools from sources you do not fully trust.": "Não instale ferramentas de origens nas quais você não confia totalmente.", "Docling": "", "Docling Server URL required.": "URL do servidor Docling necessária.", "Document": "Documento", "Document Intelligence": "Inteligência de documentos", "Document Intelligence endpoint required.": "É necessário o endpoint do Document Intelligence.", "Documentation": "Documentação", "Documents": "Documentos", "does not make any external connections, and your data stays securely on your locally hosted server.": "não faz nenhuma conexão externa, e seus dados permanecem seguros no seu servidor local.", "Domain Filter List": "Lista de filtros de domínio", "don't fetch random pipelines from sources you don't trust.": "Não busque pipelines aleatórios de fontes não confiáveis.", "Don't have an account?": "Não tem uma conta?", "don't install random functions from sources you don't trust.": "não instale funções aleatórias de origens que você não confia.", "don't install random tools from sources you don't trust.": "não instale ferramentas aleatórias de origens que você não confia.", "Don't like the style": "Não gosta do estilo", "Done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Download": "Baixar", "Download & Delete": "Baixar e excluir", "Download as SVG": "Baixar como SVG", "Download canceled": "Download cancelado", "Download Database": "Download do Banco de Dados", "Drag and drop a file to upload or select a file to view": "Arraste e solte um arquivo para enviar ou selecione um arquivo para visualizar", "Draw": "Empate", "Drop any files here to upload": "Solte qualquer arquivo aqui para fazer upload", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "por exemplo, '30s', '10m'. Unidades de tempo válidas são 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "por exemplo, \"json\" ou um esquema JSON", "e.g. 60": "por exemplo, 60", "e.g. A filter to remove profanity from text": "Exemplo: Um filtro para remover palavrões do texto", "e.g. en": "por exemplo, en", "e.g. My Filter": "Exemplo: <PERSON><PERSON>", "e.g. My Tools": "Exemplo: Minhas Ferramentas", "e.g. my_filter": "Exemplo: my_filter", "e.g. my_tools": "Exemplo: my_tools", "e.g. pdf, docx, txt": "por exemplo, pdf, docx, txt", "e.g. Tools for performing various operations": "Exemplo: Ferramentas para executar operações diversas", "e.g., 3, 4, 5 (leave blank for default)": "por exemplo, 3, 4, 5 (deixe em branco para o padrão)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "por exemplo, áudio/wav, áudio/mpeg, vídeo/* (deixe em branco para os padrões)", "e.g., en-US,ja-JP (leave blank for auto-detect)": "por exemplo, en-US, ja-JP (deixe em branco para detecção automática)", "e.g., westus (leave blank for eastus)": "por exemplo, westus (deixe em branco para eastus)", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Editar Arena de Modelos", "Edit Channel": "Editar Canal", "Edit Connection": "<PERSON><PERSON>", "Edit Default Permissions": "<PERSON>ar <PERSON>", "Edit Folder": "<PERSON><PERSON>", "Edit Memory": "<PERSON><PERSON>", "Edit User": "<PERSON><PERSON>", "Edit User Group": "Editar Grupo de Usuários", "edited": "editado", "Edited": "Editado", "Editing": "<PERSON><PERSON><PERSON>", "Eject": "<PERSON><PERSON><PERSON>", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "Embarque em aventuras", "Embedding": "Embedding", "Embedding Batch Size": "Tamanho do Lote de Embedding", "Embedding Model": "Modelo de Embedding", "Embedding Model Engine": "Motor do Modelo de Embedding", "Embedding model set to \"{{embedding_model}}\"": "Modelo de embedding definido para \"{{embedding_model}}\"", "Enable API Key": "Habilitar chave de API", "Enable autocomplete generation for chat messages": "Habilitar geração de preenchimento automático para mensagens do chat", "Enable Code Execution": "Habilitar execução de código", "Enable Code Interpreter": "Habilitar intérprete de código", "Enable Community Sharing": "Ativar Compartilhamento com a Comunidade", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Habilite o bloqueio de memória (mlock) para evitar que os dados do modelo sejam transferidos da RAM para a área de troca (swap). Essa opção bloqueia o conjunto de páginas em uso pelo modelo na RAM, garantindo que elas não sejam transferidas para o disco. Isso pode ajudar a manter o desempenho, evitando falhas de página e garantindo acesso rápido aos dados.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Habilite o mapeamento de memória (mmap) para carregar dados do modelo. Esta opção permite que o sistema use o armazenamento em disco como uma extensão da RAM, tratando os arquivos do disco como se estivessem na RAM. Isso pode melhorar o desempenho do modelo, permitindo acesso mais rápido aos dados. No entanto, pode não funcionar corretamente com todos os sistemas e consumir uma quantidade significativa de espaço em disco.", "Enable Message Rating": "Ativar Avaliação de Mensagens", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Ativar Novos Cadastros", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "<PERSON><PERSON><PERSON>, desabilite ou personalize as tags de raciocínio usadas pelo modelo. \"Enabled\" usa tags padrão, \"Disabled\" desativa as tags de raciocínio e \"Custom\" permite que você especifique suas próprias tags de início e fim.", "Enabled": "<PERSON><PERSON>do", "End Tag": "Tag final", "Endpoint URL": "", "Enforce Temporary Chat": "Aplicar chat tempor<PERSON>rio", "Enhance": "<PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Certifique-se de que seu arquivo CSV inclua 4 colunas nesta ordem: Nome, Email, Senha, Função.", "Enter {{role}} message here": "Digite a mensagem de {{role}} aqui", "Enter a detail about yourself for your LLMs to recall": "Digite um detalhe sobre você para seus LLMs lembrarem", "Enter a title for the pending user info overlay. Leave empty for default.": "Insira um título para a sobreposição de informações pendentes do usuário. Deixe em branco como padrão.", "Enter a watermark for the response. Leave empty for none.": "Insira uma marca d'água para a resposta. Deixe em branco se não houver nenhuma.", "Enter additional headers in JSON format": "", "Enter additional headers in JSON format (e.g. {{'{{\"X-Custom-Header\": \"value\"}}'}})": "", "Enter additional parameters in JSON format": "", "Enter api auth string (e.g. username:password)": "Digite a string de autenticação da API (por exemplo, username:password)", "Enter Application DN": "Digite o DN da Aplicação", "Enter Application DN Password": "Digite a Senha do DN da Aplicação", "Enter Bing Search V7 Endpoint": "Digite o Endpoint do Bing Search V7", "Enter Bing Search V7 Subscription Key": "Digite a Chave de Assinatura do Bing Search V7", "Enter Bocha Search API Key": "Insira a chave da API de pesquisa do Bocha", "Enter Brave Search API Key": "Insira a chave da API de pesquisa do Brave", "Enter certificate path": "Digite o caminho do certificado", "Enter CFG Scale (e.g. 7.0)": "Digite a escala de CFG (por exemplo, 7.0)", "Enter Chunk Overlap": "Digite a Sobreposição de Chunk", "Enter Chunk Size": "Digite o Tamanho do Chunk", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Insira pares \"token:bias_value\" separados por vírgulas (exemplo: 5432:100, 413:-100)", "Enter Config in JSON format": "Insira a configuração no formato JSON", "Enter content for the pending user info overlay. Leave empty for default.": "Insira o conteúdo para a sobreposição de informações pendentes do usuário. Deixe em branco para o padrão.", "Enter coordinates (e.g. 51.505, -0.09)": "<PERSON><PERSON><PERSON> as coordena<PERSON> (por exemplo, 51,505, -0,09)", "Enter Datalab Marker API Base URL": "Insira o URL base da API do marcador do Datalab", "Enter Datalab Marker API Key": "Insira a chave da API do marcador do Datalab", "Enter description": "Digite a descrição", "Enter Docling OCR Engine": "Insira a Engine OCR do Docling", "Enter Docling OCR Language(s)": "Insira o(s) idioma(s) do Docling OCR", "Enter Docling Server URL": "Digite a URL do servidor Docling", "Enter Document Intelligence Endpoint": "Insira o endpoint do Document Intelligence", "Enter Document Intelligence Key": "Insira a chave de inteligência do documento", "Enter domains separated by commas (e.g., example.com,site.org)": "Insira domínios separados por vírgulas (por exemplo, example.com,site.org)", "Enter Exa API Key": "Insira a chave da API Exa", "Enter External Document Loader API Key": "Insira a chave da API do carregador de documentos externo", "Enter External Document Loader URL": "Insira a URL do carregador de documentos externo", "Enter External Web Loader API Key": "Insira a chave da API do carregador da Web externo", "Enter External Web Loader URL": "Insira a URL do carregador da Web externo", "Enter External Web Search API Key": "Insira a chave da API de pesquisa na Web externa", "Enter External Web Search URL": "Insira a URL de pesquisa na Web externa", "Enter Firecrawl API Base URL": "Insira a URL base da API do Firecrawl", "Enter Firecrawl API Key": "Insira a chave da API do Firecrawl", "Enter folder name": "Digite o nome da pasta", "Enter Github Raw URL": "Digite a URL bruta do Github", "Enter Google PSE API Key": "Digite a Chave API do Google PSE", "Enter Google PSE Engine Id": "Digite o ID do Motor do Google PSE", "Enter hex color (e.g. #FF0000)": "Digite a cor hexadecimal (por exemplo, #FF0000)", "Enter ID": "Digite o <PERSON>", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON> o Tam<PERSON>ho da <PERSON>m (por exemplo, 512x512)", "Enter Jina API Key": "Digite a Chave API Jina", "Enter JSON config (e.g., {\"disable_links\": true})": "Insira a configuração JSON (por exemplo, {\"disable_links\": true})", "Enter Jupyter Password": "Digite a senha do Jupyter", "Enter Jupyter Token": "<PERSON>sira o Token Jupyter", "Enter Jupyter URL": "Insira a URL do Jupyter", "Enter Kagi Search API Key": "Insira a chave da API de pesquisa do Kagi", "Enter Key Behavior": "Comportamento da tecla Enter", "Enter language codes": "Digite os códigos de idioma", "Enter Mistral API Key": "Insira a chave da API Mistral", "Enter Model ID": "Digite o ID do modelo", "Enter model tag (e.g. {{modelTag}})": "Digite a tag do modelo (por exemplo, {{modelTag}})", "Enter Mojeek Search API Key": "Digite a Chave API do Mojeek Search", "Enter name": "Digite o nome", "Enter New Password": "Digite uma nova senha", "Enter Number of Steps (e.g. 50)": "Digite o Número de Passos (por exemplo, 50)", "Enter Ollama Cloud API Key": "Insira a chave da API do Ollama Cloud", "Enter Perplexity API Key": "Insira a chave da API Perplexity", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Insira a URL do proxy (por exemplo, https://usuário:senha@host:porta)", "Enter reasoning effort": "Enter reasoning effort", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON> o Sampler (por exemplo, Euler a)", "Enter Scheduler (e.g. Karras)": "Digite o Agendador (por exemplo, Karras)", "Enter Score": "Digite a Pontuação", "Enter SearchApi API Key": "Digite a Chave API do SearchApi", "Enter SearchApi Engine": "Digite o Motor do SearchApi", "Enter Searxng Query URL": "Digite a URL de Consulta do Searxng", "Enter Seed": "Digite a <PERSON>d", "Enter SerpApi API Key": "Insira a chave da API SerpApi", "Enter SerpApi Engine": "Digite o mecanismo/engine SerpApi", "Enter Serper API Key": "Digite a Chave API do Serper", "Enter Serply API Key": "Digite a Chave API do Serply", "Enter Serpstack API Key": "Digite a Chave API do Serpstack", "Enter server host": "Digite o host do servidor", "Enter server label": "Digite o label do servidor", "Enter server port": "Digite a porta do servidor", "Enter Sougou Search API sID": "Insira o sID da API de pesquisa do Sougou", "Enter Sougou Search API SK": "Digite Sougou Search API SK", "Enter stop sequence": "Digite a sequência de parada", "Enter system prompt": "Digite o prompt do sistema", "Enter system prompt here": "Insira o prompt do sistema aqui", "Enter Tavily API Key": "Digite a Chave API do Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Insira a URL pública da sua WebUI. Esta URL será usada para gerar links nas notificações.", "Enter the URL of the function to import": "Digite a URL da função a ser importada", "Enter the URL to import": "Digite a URL para importar", "Enter Tika Server URL": "Digite a URL do Servidor Tika", "Enter timeout in seconds": "Insira o tempo limite em segundos", "Enter to Send": "Enter para Enviar", "Enter Top K": "Digite o Top K", "Enter Top K Reranker": "<PERSON><PERSON><PERSON> o <PERSON> K Reranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "Digite a URL (por exemplo, http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Digite a URL (por exemplo, http://localhost:11434)", "Enter value": "Insira o valor", "Enter value (true/false)": "Insira o valor (true/false)", "Enter Yacy Password": "Digite a senha do Yacy", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Digite a URL do Yacy (por exemplo, http://yacy.example.com:8090)", "Enter Yacy Username": "Digite o nome de usuário do <PERSON>cy", "Enter your code here...": "Insira seu código aqui...", "Enter your current password": "Digite sua senha atual", "Enter Your Email": "<PERSON><PERSON><PERSON>", "Enter Your Full Name": "Digite Seu Nome Completo", "Enter your gender": "Digite seu gênero", "Enter your message": "Digite sua mensagem", "Enter your name": "Digite seu nome", "Enter Your Name": "<PERSON><PERSON><PERSON> Seu Nome", "Enter your new password": "Digite sua nova senha", "Enter Your Password": "<PERSON><PERSON><PERSON>", "Enter Your Role": "Digite Sua Função", "Enter Your Username": "Digite seu usuário", "Enter your webhook URL": "Insira a URL do seu webhook", "Entra ID": "ID Entra", "Error": "Erro", "ERROR": "ERRO", "Error accessing directory": "Erro ao acessar o diretório", "Error accessing Google Drive: {{error}}": "Erro ao acessar o Google Drive: {{error}}", "Error accessing media devices.": "Erro ao acessar dispositivos de mídia.", "Error starting recording.": "Erro ao iniciar a gravação.", "Error unloading model: {{error}}": "Erro ao descarregar modelo: {{error}}", "Error uploading file: {{error}}": "Erro ao carregar o arquivo: {{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "Erro: Já existe um modelo com o ID '{{modelId}}'. Selecione um ID diferente para prosseguir.", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "Erro: O ID do modelo não pode estar vazio. Insira um ID válido para prosseguir.", "Evaluations": "Avaliações", "Everyone": "Todos", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Exemplo: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Exemplo: ALL", "Example: mail": "Exemplo: Email", "Example: ou=users,dc=foo,dc=example": "Exemplo: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Exemplo: sAMAccountName ou uid ou userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Excedeu o número de licenças disponíveis. Entre em contato com o suporte para aumentar o número de licenças.", "Exclude": "Excluir", "Execute code for analysis": "Executar código para análise", "Executing **{{NAME}}**...": "Executando **{{NAME}}**...", "Expand": "Expandir", "Experimental": "Experimental", "Explain": "Explicar", "Explore the cosmos": "Explorar o cosmos", "Export": "Exportar", "Export All Archived Chats": "Exportar todos os chats arquivados", "Export All Chats (All Users)": "Exportar Todos os Chats (Todos os Usuários)", "Export chat (.json)": "Exportar chat (.json)", "Export Chats": "Exportar Chats", "Export Config to JSON File": "Exportar Configuração para Arquivo JSON", "Export Presets": "Exportar Presets", "Export Prompt Suggestions": "Exportar Sugestões de Prompt", "Export to CSV": "Exportar para CSV", "Export Users": "Exportar Usuários", "External": "Externo", "External Document Loader URL required.": "URL do carregador de documentos externo necessária.", "External Task Model": "Modelo de Tarefa Externa", "External Tools": "Ferramentas Externas", "External Web Loader API Key": "Chave de API do carregador da Web externo", "External Web Loader URL": "URL do carregador da Web externo", "External Web Search API Key": "Chave de API de pesquisa na Web externa", "External Web Search URL": "URL de pesquisa na Web externa", "Fade Effect for Streaming Text": "Efeito de desbotamento para texto em streaming", "Failed to add file.": "Falha ao adicionar arquivo.", "Failed to connect to {{URL}} OpenAPI tool server": "Falha ao conectar ao servidor da ferramenta OpenAPI {{URL}}", "Failed to copy link": "Falha ao copiar o link", "Failed to create API Key.": "Falha ao criar a Chave API.", "Failed to delete note": "Falha ao excluir a nota", "Failed to extract content from the file: {{error}}": "Falha ao extrair conteúdo do arquivo: {{error}}", "Failed to extract content from the file.": "Falha ao extrair conteúdo do arquivo.", "Failed to fetch models": "Falha ao buscar modelos", "Failed to generate title": "Falha ao gerar tí<PERSON>lo", "Failed to import models": "Falha ao importar modelos", "Failed to load chat preview": "Falha ao carregar a pré-visualização do chat", "Failed to load file content.": "Falha ao carregar o conteúdo do arquivo.", "Failed to move chat": "Falha ao mover o chat", "Failed to read clipboard contents": "Falha ao ler o conteúdo da área de transferência", "Failed to render diagram": "", "Failed to save connections": "Falha ao salvar <PERSON>", "Failed to save conversation": "<PERSON>alha ao salvar a conversa", "Failed to save models configuration": "Falha ao salvar a configuração dos modelos", "Failed to update settings": "<PERSON>al<PERSON> ao atualizar as configuraç<PERSON><PERSON>", "Failed to upload file.": "Falha ao carregar o arquivo.", "fast": "<PERSON><PERSON><PERSON><PERSON>", "Features": "Funcionalidades", "Features Permissions": "Permissões das Funcionalidades", "February": "<PERSON><PERSON>", "Feedback Details": "Detalhes do comentário", "Feedback History": "Histórico de comentários", "Feedbacks": "Comentários", "Feel free to add specific details": "Sinta-se <PERSON> vontade para adicionar detalhes específicos", "Female": "Feminino", "File": "Arquivo", "File added successfully.": "Arquivo adicionado com sucesso.", "File content updated successfully.": "Arquivo de conteúdo atualizado com sucesso.", "File Mode": "Modo de Arquivo", "File not found.": "Arquivo não encontrado.", "File removed successfully.": "Arquivo removido com sucesso.", "File size should not exceed {{maxSize}} MB.": "Arquivo não pode exceder {{maxSize}} MB.", "File Upload": "Upload de arquivo", "File uploaded successfully": "Arquivo carregado com sucesso", "Files": "<PERSON>r<PERSON><PERSON>", "Filter": "Filtro", "Filter is now globally disabled": "O filtro está agora desativado globalmente", "Filter is now globally enabled": "O filtro está agora ativado globalmente", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Falsificação de impressão digital detectada: Não foi possível usar as iniciais como avatar. Usando a imagem de perfil padrão.", "Firecrawl API Base URL": "URL base da <PERSON> do Firecrawl", "Firecrawl API Key": "Chave de API do Firecrawl", "Floating Quick Actions": "Ações rápidas flutuantes", "Focus chat input": "Focar entrada de chat", "Folder": "", "Folder Background Image": "Imagem de fundo da pasta", "Folder deleted successfully": "Pasta excluída com sucesso", "Folder Name": "<PERSON>me da Pasta", "Folder name cannot be empty.": "Nome da pasta não pode estar vazio.", "Folder name updated successfully": "Nome da pasta atualizado com sucesso", "Folder updated successfully": "Pasta atualizada com sucesso", "Folders": "Pastas", "Follow up": "Acompanhamento", "Follow Up Generation": "Geração de Acompanhamento", "Follow Up Generation Prompt": "Prompt para Geração dos Acompanhamentos", "Follow-Up Auto-Generation": "Geração automática de acompanhamento", "Followed instructions perfectly": "<PERSON><PERSON><PERSON> as instruções perfeitamente", "Force OCR": "Forçar OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "Forçar OCR em todas as páginas do PDF. <PERSON><PERSON> pode levar a resultados piores se você tiver texto de boa qualidade nos seus PDFs. O padrão é Falso.", "Forge new paths": "Trilhar novos caminhos", "Form": "<PERSON><PERSON><PERSON><PERSON>", "Format Lines": "Formatar linhas", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "Formata as linhas na saída. O padrão é Falso. Se definido como Verdadeiro, as linhas serão formatadas para detectar matemática e estilos embutidos.", "Format your variables using brackets like this:": "Formate suas variáveis usando duplas chaves como este:", "Formatting may be inconsistent from source.": "A formatação pode ser inconsistente em relação à fonte.", "Forwards system user OAuth access token to authenticate": "Encaminha o token de acesso OAuth do usuário do sistema para autenticação", "Forwards system user session credentials to authenticate": "<PERSON><PERSON><PERSON><PERSON> as credenciais da sessão do usuário do sistema para autenticação", "Full Context Mode": "Modo de contexto completo", "Function": "Função", "Function Calling": "Chamada de função", "Function created successfully": "Função criada com sucesso", "Function deleted successfully": "Função excluída com sucesso", "Function Description": "Descrição da Função", "Function ID": "ID da Função", "Function imported successfully": "Função importada com sucesso", "Function is now globally disabled": "A função está agora desativada globalmente", "Function is now globally enabled": "A função está agora ativada globalmente", "Function Name": "Nome da Função", "Function updated successfully": "Função atualizada com sucesso", "Functions": "Funções", "Functions allow arbitrary code execution.": "Funções permitem a execução arbitrária de código.", "Functions imported successfully": "Funções importadas com sucesso", "Gemini": "", "Gemini API Config": "Configuração da API Gemini", "Gemini API Key is required.": "A chave da API Gemini é necessária.", "Gender": "<PERSON><PERSON><PERSON><PERSON>", "General": "G<PERSON>", "Generate": "<PERSON><PERSON><PERSON>", "Generate an image": "<PERSON>erar uma imagem", "Generate Image": "<PERSON><PERSON><PERSON>", "Generate prompt pair": "Gerar par de prompts", "Generated Image": "Imagem gerada", "Generating search query": "Gerando consulta de pesquisa", "Generating...": "Gerando...", "Get information on {{name}} in the UI": "Obtenha informações sobre {{name}} na IU", "Get started": "Iniciar", "Get started with {{WEBUI_NAME}}": "Iniciar com {{WEBUI_NAME}}", "Global": "Global", "Good Response": "Boa Resposta", "Google Drive": "", "Google PSE API Key": "Chave API do Google PSE", "Google PSE Engine Id": "ID do Motor do Google PSE", "Gravatar": "", "Group": "Grupo", "Group created successfully": "Grupo criado com sucesso", "Group deleted successfully": "Grupo excluído com sucesso", "Group Description": "Descrição do Grupo", "Group Name": "Nome do Grupo", "Group updated successfully": "Grupo atualizado com sucesso", "Groups": "Grupos", "H1": "<PERSON><PERSON><PERSON><PERSON>", "H2": "Subtítulo", "H3": "Sub-subtítulos", "Haptic Feedback": "<PERSON><PERSON><PERSON>", "Headers": "", "Headers must be a valid JSON object": "", "Height": "Altura", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Ajude-nos a criar o melhor ranking da comunidade compartilhando sua historia de comentários!", "Hex Color": "Cor hexadecimal", "Hex Color - Leave empty for default color": "Cor Hexadecimal - Deixe em branco para a cor padrão", "Hide": "Ocultar", "Hide from Sidebar": "Ocultar da barra lateral", "Hide Model": "Ocultar modelo", "High": "Alto", "High Contrast Mode": "<PERSON>do de alto contraste", "Home": "Início", "Host": "<PERSON><PERSON><PERSON>", "How can I help you today?": "Como posso ajudar você hoje?", "How would you rate this response?": "Como você avalia essa resposta?", "HTML": "", "Hybrid Search": "Pesquisa Híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Eu reconheço que li e entendi as implicações da minha ação. Estou ciente dos riscos associados à execução de código arbitrário e verifiquei a confiabilidade da fonte.", "ID": "", "ID cannot contain \":\" or \"|\" characters": "O ID não pode conter caracteres \":\" ou \"|\"", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Desperte a curiosidade", "Image": "Imagem", "Image Compression": "Compressão de imagem", "Image Compression Height": "Altura de compressão da imagem", "Image Compression Width": "Largura de compressão de imagem", "Image Generation": "Geração de Imagem", "Image Generation (Experimental)": "Geração de Imagem (Experimental)", "Image Generation Engine": "Motor de Geração de Imagem", "Image Max Compression Size": "Tamanho máximo de compressão da imagem", "Image Max Compression Size height": "Altura do tamanho máximo de compressão da imagem", "Image Max Compression Size width": "Tamanho máximo de compressão da imagem largura", "Image Prompt Generation": "Geração de prompt de imagem", "Image Prompt Generation Prompt": "Prompt de geração de prompt de imagem", "Image Settings": "Configurações de Imagem", "Images": "Imagens", "Import": "Importar", "Import Chats": "Importar Chats", "Import Config from JSON File": "Importar Configurações de JSON", "Import From Link": "Importar do link", "Import Notes": "Importar Notas", "Import Presets": "Importar Presets", "Import Prompt Suggestions": "Importar Sugestões de Prompt", "Import successful": "Importação bem-sucedida", "Important Update": "Atualização importante", "In order to force OCR, performing OCR must be enabled.": "Para forçar o OCR, a execução do OCR deve estar habilitada.", "Include": "Incluir", "Include `--api-auth` flag when running stable-diffusion-webui": "Incluir a flag `--api-auth` ao executar stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Incluir a flag `--api` ao executar stable-diffusion-webui", "Includes SharePoint": "Inclui o SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Influencia a rapidez com que o algoritmo responde ao feedback do texto gerado. Uma taxa de aprendizado menor resultará em ajustes mais lentos, enquanto uma taxa de aprendizado maior tornará o algoritmo mais responsivo.", "Info": "Informação", "Initials": "Iniciais", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Injete todo o conteúdo como contexto para processamento abrangente; isso é recomendado para consultas complexas.", "Input": "Entrada", "Input commands": "Comandos de entrada", "Input Key (e.g. text, unet_name, steps)": "Chave de entrada (por exemplo, texto, unet_name, etapas)", "Input Variables": "Variáveis de entrada", "Insert": "Inserir", "Insert Follow-Up Prompt to Input": "Inserir prompt de acompanhamento para entrada", "Insert Prompt as Rich Text": "Inserir prompt como texto enriquecido", "Insert Suggestion Prompt to Input": "Inserir prompt de sugestão para entrada", "Install from Github URL": "Instalar da URL do Github", "Instant Auto-Send After Voice Transcription": "Envio Automático Instantâneo Após Transcrição de Voz", "Integration": "Integração", "Integrations": "Integrações", "Interface": "Interface", "Invalid file content": "Conteúdo de arquivo inválido", "Invalid file format.": "Formato de arquivo inválido.", "Invalid JSON file": "Arquivo JSON inválido", "Invalid JSON format for ComfyUI Workflow.": "Formato JSON inválido para o fluxo de trabalho do ComfyUI.", "Invalid JSON format for Parameters": "", "Invalid JSON format in Additional Config": "Formato JSON inválido na configuração adicional", "Invalid Tag": "Tag Inválida", "is typing...": "está digitando...", "Italic": "Itálico", "January": "Janeiro", "Jina API Key": "Chave de <PERSON>", "join our Discord for help.": "junte-se ao nosso Discord para ajudar.", "JSON": "JSON", "JSON Preview": "Pré-visualização JSON", "JSON Spec": "Especificação JSON", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "Expiração do JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Follow-Up Prompts in Chat": "<PERSON><PERSON><PERSON> prompts de acompanhamento no chat", "Keep in Sidebar": "Manter na barra lateral", "Key": "Chave", "Key is required": "Chave é obrigatória", "Keyboard shortcuts": "Atalhos de Teclado", "Knowledge": "Conhecimento", "Knowledge Access": "Acesso ao Conhecimento", "Knowledge Base": "Base de Conhecimento", "Knowledge created successfully.": "Conhecimento criado com sucesso.", "Knowledge deleted successfully.": "Conhecimento excluído com sucesso.", "Knowledge Description": "Descrição da Base de Conhecimento", "Knowledge Name": "Nome da Base de Conhecimento", "Knowledge Public Sharing": "Compartilhamento Público da Base de Conhecimento", "Knowledge reset successfully.": "Conhecimento resetado com sucesso.", "Knowledge updated successfully": "Conhecimento atualizado com sucesso", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "<PERSON><PERSON><PERSON><PERSON>", "Landing Page Mode": "Modo Landing Page", "Language": "Idioma", "Language Locales": "Locais de idioma", "Last Active": "Última Atividade", "Last Modified": "Última Modificação", "Last reply": "Última resposta", "LDAP": "", "LDAP server updated": "Servidor LDAP atualizado", "Leaderboard": "Tabela de classificação", "Learn More": "<PERSON><PERSON> Mai<PERSON>", "Learn more about OpenAPI tool servers.": "Saiba mais sobre servidores de ferramentas OpenAPI.", "Leave empty for no compression": "Deixe em branco para nenhuma compactação", "Leave empty for unlimited": "Deixe vazio para ilimitado", "Leave empty to include all models from \"{{url}}\" endpoint": "Deixe em branco para incluir todos os modelos do ponto final \"{{url}}\"", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "Deixe em branco para incluir todos os modelos do ponto final \"{{url}}/api/tags\"", "Leave empty to include all models from \"{{url}}/models\" endpoint": "Deixe em branco para incluir todos os modelos do ponto final \"{{url}}/models\"", "Leave empty to include all models or select specific models": "Deixe vazio para incluir todos os modelos ou selecione modelos especificos", "Leave empty to use the default prompt, or enter a custom prompt": "Deixe vazio para usar o prompt padr<PERSON>, ou insira um prompt personalizado", "Leave model field empty to use the default model.": "Deixe o campo do modelo vazio para usar o modelo padrão.", "Legacy": "<PERSON><PERSON>", "lexical": "", "License": "Licença", "Lift List": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Escutando...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLMs podem cometer erros. Verifique informações importantes.", "Loader": "Carregador", "Loading Kokoro.js...": "Carregando Kokoro.js...", "Loading...": "Carregando...", "Local": "", "Local Task Model": "Modelo de Tarefa Local", "Location access not allowed": "Acesso ao local não permitido", "Lost": "<PERSON><PERSON><PERSON>", "Low": "Baixo", "LTR": "Esquerda para Direita", "Made by Open WebUI Community": "<PERSON>ito pela Comunidade OpenWebUI", "Make password visible in the user interface": "Tornar a senha visível na interface do usuário", "Make sure to enclose them with": "Certifique-se de encerrá-los com", "Make sure to export a workflow.json file as API format from ComfyUI.": "Certifique-se de exportar um arquivo workflow.json como o formato API do ComfyUI.", "Male": "<PERSON><PERSON><PERSON><PERSON>", "Manage": "Gerenciar", "Manage Direct Connections": "Gerenciar conexões diretas", "Manage Models": "Gerenciar modelos", "Manage Ollama": "Gerenciar <PERSON>", "Manage Ollama API Connections": "Gerenciar Conexões Ollama API", "Manage OpenAI API Connections": "Gerenciar Conexões OpenAI API", "Manage Pipelines": "Gerenciar Pipelines", "Manage Tool Servers": "Gerenciar servidores de ferramentas", "Manage your account information.": "<PERSON><PERSON><PERSON><PERSON> as informações da sua conta.", "March": "Março", "Markdown": "", "Markdown (Header)": "Markdown (Cabeçalho)", "Max Speakers": "", "Max Upload Count": "Quantidade máxima de anexos", "Max Upload Size": "Tamanho máximo do arquivo", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Máximo de 3 modelos podem ser baixados simultaneamente. Por favor, tente novamente mais tarde.", "May": "<PERSON><PERSON>", "MCP": "", "MCP support is experimental and its specification changes often, which can lead to incompatibilities. OpenAPI specification support is directly maintained by the Open WebUI team, making it the more reliable option for compatibility.": "O suporte ao MCP é experimental e suas especificações mudam com frequência, o que pode levar a incompatibilidades. O suporte à especificação OpenAPI é mantido diretamente pela equipe do Open WebUI, tornando-o a opção mais confiável para compatibilidade.", "Medium": "Médio", "Memories accessible by LLMs will be shown here.": "Memórias acessíveis por LLMs serão mostradas aqui.", "Memory": "Memória", "Memory added successfully": "Memória adicionada com sucesso", "Memory cleared successfully": "Memória limpa com sucesso", "Memory deleted successfully": "Memória excluída com sucesso", "Memory updated successfully": "Memória atualizada com sucesso", "Merge Responses": "Mesclar respostas", "Merged Response": "Resposta Mesclada", "Message rating should be enabled to use this feature": "Mensagem de avaliação deve estar habilitada para usar esta função", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Mensagens enviadas após criar seu link não serão compartilhadas. Usuários com o URL poderão visualizar o chat compartilhado.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "Chave de API do Mistral OCR necessária.", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Modelo '{{modelName}}' foi baixado com sucesso.", "Model '{{modelTag}}' is already in queue for downloading.": "Modelo '{{modelTag}}' já está na fila para download.", "Model {{modelId}} not found": "Modelo {{modelId}} não encontrado", "Model {{modelName}} is not vision capable": "Modelo {{modelName}} não é capaz de visão", "Model {{name}} is now {{status}}": "Modelo {{name}} est<PERSON> agora {{status}}", "Model {{name}} is now hidden": "O modelo {{name}} agora está oculto", "Model {{name}} is now visible": "O modelo {{name}} agora está visível", "Model accepts file inputs": "O modelo aceita entradas de arquivo", "Model accepts image inputs": "Modelo aceita entradas de imagens", "Model can execute code and perform calculations": "O modelo pode executar código e realizar cálculos", "Model can generate images based on text prompts": "O modelo pode gerar imagens com base em prompts de texto", "Model can search the web for information": "O modelo pode pesquisar informações na web", "Model created successfully!": "Modelo criado com sucesso!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Caminho do sistema de arquivos do modelo detectado. Nome curto do modelo é necessário para atualização, não é possível continuar.", "Model Filtering": "Filtrando modelo", "Model ID": "ID do Modelo", "Model ID is required.": "É necessário o ID do modelo.", "Model IDs": "IDs do modelo", "Model Name": "Nome do Modelo", "Model name already exists, please choose a different one": "O nome do modelo já existe, escolha um diferente", "Model Name is required.": "O nome do modelo é obrigatório.", "Model not selected": "Modelo não selecionado", "Model Params": "Parâmetros do Modelo", "Model Permissions": "Permissões do Modelo", "Model unloaded successfully": "Modelo descarregado com sucesso", "Model updated successfully": "Modelo atualizado com sucesso", "Model(s) do not support file upload": "Modelo(s) não suportam upload de arquivo", "Modelfile Content": "Conteúdo do Arquivo do Modelo", "Models": "Modelos", "Models Access": "Acesso aos Modelos", "Models configuration saved successfully": "Configuração de modelos salva com sucesso", "Models imported successfully": "", "Models Public Sharing": "Modelos de Compartilhamento Público", "Mojeek Search API Key": "Chave de API Mojeel Search", "More": "<PERSON><PERSON>", "More Concise": "<PERSON><PERSON> conciso", "More Options": "<PERSON><PERSON>", "Move": "Mover", "Name": "Nome", "Name and ID are required, please fill them out": "Nome e documento de identidade são obrigatórios, por favor preencha-os", "Name your knowledge base": "Nome da sua base de conhecimento", "Native": "Nativo", "New Button": "Novo Botão", "New Chat": "Novo Chat", "New Folder": "Nova Pasta", "New Function": "Nova Função", "New Knowledge": "", "New Model": "", "New Note": "<PERSON>a Nota", "New Password": "Nova Senha", "New Prompt": "", "New Tool": "Nova Ferrameta", "new-channel": "novo-canal", "Next message": "Próxima mensagem", "No authentication": "Sem autenticação", "No chats found": "<PERSON>enhum chat encontrado", "No chats found for this user.": "Nenhum chat encontrado para este usuário.", "No chats found.": "<PERSON><PERSON><PERSON> chat encontrado.", "No content": "<PERSON><PERSON><PERSON> con<PERSON>", "No content found": "Nenhum conteúdo encontrado", "No content found in file.": "Nenhum conteúdo encontrado no arquivo.", "No content to speak": "Sem conteúdo para falar", "No conversation to save": "Nenhuma conversa para salvar", "No distance available": "Sem distância disponível", "No feedbacks found": "Comentários não encontrados", "No file selected": "Nenhum arquivo selecionado", "No functions found": "", "No groups with access, add a group to grant access": "Nenhum grupo com acesso, adicione um grupo para dar acesso", "No HTML, CSS, or JavaScript content found.": "Nenhum conteúdo HTML, CSS ou JavaScript encontrado.", "No inference engine with management support found": "Nenhum mecanismo de inferência com suporte de gerenciamento encontrado", "No knowledge found": "Nenhum conhecimento encontrado", "No memories to clear": "Nenhuma memória para limpar", "No model IDs": "Nenhum ID de modelo", "No models found": "Nenhum modelo encontrado", "No models selected": "Nenhum modelo selecionado", "No Notes": "<PERSON><PERSON>", "No notes found": "Notas não encontradas", "No prompts found": "", "No results": "Nenhum resultado encontrado", "No results found": "Nenhum resultado encontrado", "No search query generated": "Nenhuma consulta de pesquisa gerada", "No source available": "Nenhuma fonte disponível", "No sources found": "Nenhuma fonte encontrada", "No suggestion prompts": "Sem prompts sugeridos", "No tools found": "", "No users were found.": "Nenhum usuário foi encontrado.", "No valves": "Sem configurações", "No valves to update": "Nenhuma configuração para atualizar", "Node Ids": "", "None": "<PERSON><PERSON><PERSON>", "Not factually correct": "Não está factualmente correto", "Not helpful": "Não é útil", "Not Registered": "Não registrado", "Note": "<PERSON>a", "Note deleted successfully": "Nota excluída com sucesso", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Se você definir uma pontuação mínima, a pesquisa retornará apenas documentos com pontuação igual ou superior à pontuação mínima.", "Notes": "Notas", "Notes Public Sharing": "Compartilhamento Público das Notas", "Notification Sound": "Som de notificação", "Notification Webhook": "Webhook de notificação", "Notifications": "Notificações", "November": "Novembro", "OAuth": "", "OAuth 2.1": "", "OAuth ID": "OAuth ID", "October": "Out<PERSON>ro", "Off": "Des<PERSON><PERSON>", "Okay, Let's Go!": "Ok, Vamos Lá!", "OLED Dark": "OLED Escuro", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API settings updated": "Configurações da API Ollama atualizadas", "Ollama Cloud API Key": "", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "Ligado", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "Ativo somente quando a configuração \"Colar texto grande como arquivo\" estiver ativada.", "Only active when the chat input is in focus and an LLM is generating a response.": "Ativo somente quando a entrada do chat está em foco e um LLM está gerando uma resposta.", "Only alphanumeric characters and hyphens are allowed": "Somente caracteres alfanuméricos e hífens são permitidos", "Only alphanumeric characters and hyphens are allowed in the command string.": "Apenas caracteres alfanuméricos e hífens são permitidos na string de comando.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Somente coleções podem ser editadas. Crie uma nova base de conhecimento para editar/adicionar documentos.", "Only markdown files are allowed": "Somente arquivos markdown são permitidos", "Only select users and groups with permission can access": "Somente usuários e grupos selecionados com permissão podem acessar.", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ops! Parece que a URL é inválida. Por favor, verifique novamente e tente de novo.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ops! Existem arquivos a serem carregados. Por favor, aguarde que o carregamento tenha concluído.", "Oops! There was an error in the previous response.": "Ops! Houve um erro na resposta anterior.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ops! Você está usando um método não suportado (somente frontend). Por favor, sirva a WebUI a partir do backend.", "Open file": "Abrir arquivo", "Open in full screen": "A<PERSON>r em tela cheia", "Open link": "Abrir link", "Open modal to configure connection": "Abra o modal para configurar a conexão", "Open Modal To Manage Floating Quick Actions": "Abra o Modal para gerenciar ações rápidas flutuantes", "Open Modal To Manage Image Compression": "Abrir o Modal para gerenciar a compressão de imagens", "Open new chat": "Abrir novo chat", "Open Sidebar": "Abrir barra lateral", "Open User Profile Menu": "Abrir menu de perfil do usuário", "Open WebUI can use tools provided by any OpenAPI server.": "O Open WebUI pode usar ferramentas fornecidas por qualquer servidor OpenAPI.", "Open WebUI uses faster-whisper internally.": "Open WebUI usa faster-whisper internamente.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "A Open WebUI usa os embeddings de voz do SpeechT5 e do CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "A versão do Open WebUI (v{{OPEN_WEBUI_VERSION}}) é inferior à versão necessária (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Configuração da API OpenAI", "OpenAI API Key is required.": "Chave API OpenAI é necessária.", "OpenAI API settings updated": "Configurações OpenAI atualizadas", "OpenAI URL/Key required.": "URL/Chave OpenAI necessária.", "OpenAPI": "", "OpenAPI Spec": "", "openapi.json URL or Path": "", "Optional": "Opcional", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "Opções para executar um modelo de linguagem de visão local na descrição da imagem. Os parâmetros referem-se a um modelo hospedado no Hugging Face. Este parâmetro é mutuamente exclusivo com picture_description_api.", "or": "ou", "Ordered List": "Lista ordenada", "Organize your users": "Organizar seus usuários", "Other": "Outro", "OUTPUT": "SAÍDA", "Output format": "Formato de saída", "Output Format": "Formato de Saída", "Overview": "Visão Geral", "page": "p<PERSON><PERSON><PERSON>", "Paginate": "<PERSON><PERSON><PERSON>", "Parameters": "Parâmetros", "Password": "<PERSON><PERSON>", "Passwords do not match.": "As senhas não coincidem.", "Paste Large Text as File": "Cole Textos Longos como Arquivo", "PDF Backend": "", "PDF document (.pdf)": "Documento PDF (.pdf)", "PDF Extract Images (OCR)": "Extrair Imagens do PDF (OCR)", "pending": "pendente", "Pending": "Pendente", "Pending User Overlay Content": "Conteúdo de sobreposição de usuário pendente", "Pending User Overlay Title": "Título de sobreposição de usuário pendente", "Perform OCR": "Executar OCR", "Permission denied when accessing media devices": "Permissão negada ao acessar dispositivos de mídia", "Permission denied when accessing microphone": "Permissão negada ao acessar o microfone", "Permission denied when accessing microphone: {{error}}": "Permissão negada ao acessar o microfone: {{error}}", "Permissions": "Permissões", "Perplexity API Key": "Chave API da Perplexity", "Perplexity Model": "Modelo Perplexity", "Perplexity Search Context Usage": "Uso do contexto de pesquisa do Perplexity", "Personalization": "Personalização", "Picture Description API Config": "Configuração da API de descrição de imagem", "Picture Description Local Config": "Descrição da imagem Configuração local", "Picture Description Mode": "Modo de descrição de imagem", "Pin": "Fixar", "Pinned": "Fixado", "Pioneer insights": "Insights pioneiros", "Pipe": "", "Pipeline": "", "Pipeline deleted successfully": "Pipeline excluído com sucesso", "Pipeline downloaded successfully": "Pipeline baixado com sucesso", "Pipelines": "Pipelines", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines é um sistema de plugins com execução arbitrária de código —", "Pipelines Not Detected": "Pipelines Não Detectados", "Pipelines Valves": "Configurações de Pipelines", "Plain text (.md)": "Texto simples (.md)", "Plain text (.txt)": "Texto simples (.txt)", "Playground": "Playground", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Por favor, revise cuidadosamente os seguintes avisos:", "Please do not close the settings page while loading the model.": "Não feche a página de configurações enquanto estiver carregando o modelo.", "Please enter a message or attach a file.": "Por favor, insira uma mensagem ou anexe um arquivo.", "Please enter a prompt": "Por favor, digite um prompt", "Please enter a valid ID": "Por favor, insira um ID válido", "Please enter a valid JSON spec": "Por favor, insira uma especificação JSON válida", "Please enter a valid path": "Por favor, insira um caminho válido", "Please enter a valid URL": "Por favor, insira uma URL válido", "Please enter a valid URL.": "Por favor, insira uma URL válida", "Please fill in all fields.": "Por favor, preencha todos os campos.", "Please register the OAuth client": "Por favor, registre o cliente OAuth", "Please save the connection to persist the OAuth client information and do not change the ID": "Salve a conexão para persistir as informações do cliente OAuth e não altere o ID", "Please select a model first.": "Selecione um modelo primeiro.", "Please select a model.": "Selecione um modelo.", "Please select a reason": "Por favor, seleccione uma razão", "Please select a valid JSON file": "Selecione um arquivo JSON válido", "Please wait until all files are uploaded.": "Aguarde até que todos os arquivos sejam enviados.", "Port": "Porta", "Positive attitude": "Atitude positiva", "Prefer not to say": "Prefiro não dizer", "Prefix ID": "Prefixo ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "O ID de prefixo é utilizado para evitar conflitos com outras conexões, adicionando um prefixo aos IDs dos modelos - deixe em branco para desativar.", "Prevent file creation": "Impedir a criação de arquivos", "Preview": "Visualização", "Previous 30 days": "Últimos 30 dias", "Previous 7 days": "Últimos 7 dias", "Previous message": "Mensagem anterior", "Private": "Privado", "Profile": "Perfil", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (por exemplo, Diga-me um fato divertido sobre o Império Romano)", "Prompt Autocompletion": "Preenchimento automático de prompts", "Prompt Content": "Conteúdo do Prompt", "Prompt created successfully": "Prompt criado com sucesso", "Prompt suggestions": "Sugestões de Prompt", "Prompt updated successfully": "Prompt atualizado com sucesso", "Prompts": "Prompts", "Prompts Access": "Acessar prompts", "Prompts Public Sharing": "Compartilhamento Público dos Prompts", "Provider Type": "<PERSON><PERSON><PERSON>", "Public": "Público", "Pull \"{{searchValue}}\" from Ollama.com": "Obter \"{{searchValue}}\" de Ollama.com", "Pull a model from Ollama.com": "Obter um modelo de Ollama.com", "Pull Model": "", "pypdfium2": "", "Query Generation Prompt": "Prompt de Geração de Consulta", "Querying": "Consultando", "Quick Actions": "Ações <PERSON>", "RAG Template": "Modelo RAG", "Rating": "Avaliação", "Re-rank models by topic similarity": "Reclassificação de modelos por similaridade de tópico", "Read": "<PERSON>r", "Read Aloud": "Ler <PERSON> Voz Alta", "Read more →": "<PERSON><PERSON> ma<PERSON> →", "Reason": "Razão", "Reasoning Effort": "Esforço de raciocínio", "Reasoning Tags": "Tags de raciocínio", "Record": "Registro", "Record voice": "<PERSON><PERSON><PERSON> voz", "Redirecting you to Open WebUI Community": "Redirecionando você para a Comunidade OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Reduz a probabilidade de gerar respostas sem sentido. Um valor mais alto (por exemplo, 100) resultará em respostas mais diversas, enquanto um valor mais baixo (por exemplo, 10) será mais conservador.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Refira-se como \"Usuário\" (por exemplo, \"Usuário está aprendendo espanhol\")", "Reference Chats": "Chats Anteriores", "Refused when it shouldn't have": "Recusado quando não deveria", "Regenerate": "Gerar novamente", "Regenerate Menu": "<PERSON><PERSON><PERSON>", "Register Again": "Registre-se novamente", "Register Client": "Registrar cliente", "Registered": "Registrado", "Registration failed": "O registro falhou", "Registration successful": "Registro realizado com sucesso", "Reindex": "Reindexar", "Reindex Knowledge Base Vectors": "Reindexar vetores da base de conhecimento", "Release Notes": "Notas de Lançamento", "Releases": "Lançamentos", "Relevance": "Relevância", "Relevance Threshold": "<PERSON><PERSON>", "Remember Dismissal": "Lembrar da dispensa", "Remove": "Remover", "Remove {{MODELID}} from list.": "Remover {{MODELID}} da lista.", "Remove file": "Remover arquivo", "Remove File": "Remover Arquivo", "Remove image": "Remover imagem", "Remove Model": "Remover Modelo", "Remove this tag from list": "Remover esta tag da lista", "Rename": "Renomear", "Reorder Models": "Reordenar modelos", "Reply": "<PERSON><PERSON><PERSON><PERSON>", "Reply in Thread": "Responder no tópico", "Reply to thread...": "Responder ao tópico...", "Replying to {{NAME}}": "Respondendo para {{NAME}}", "required": "obrigatório", "Reranking Engine": "Motor de Reclassificação", "Reranking Model": "Modelo de Reclassificação", "Reset": "Redefinir", "Reset All Models": "Redefinir todos os modelos", "Reset Image": "Redefinir imagem", "Reset Upload Directory": "Redefinir Diretório de Upload", "Reset Vector Storage/Knowledge": "Redefinir Armazenamento de Vetores/Conhecimento", "Reset view": "Redefinir visualização", "Response": "Resposta", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Notificações de resposta não podem ser ativadas pois as permissões do site foram negadas. Por favor, visite as configurações do seu navegador para conceder o acesso necessário.", "Response splitting": "Divisão da Resposta", "Response Watermark": "Marca d'água de resposta", "Result": "<PERSON><PERSON><PERSON><PERSON>", "RESULT": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval": "Recuperação", "Retrieval Query Generation": "Geração de Consulta de Recuperação", "Retrieved {{count}} sources": "{{count}} fontes recuperadas", "Retrieved {{count}} sources_one": "", "Retrieved {{count}} sources_many": "", "Retrieved {{count}} sources_other": "", "Retrieved 1 source": "1 fonte recuperada", "Rich Text Input for Chat": "Entrada de rich text para o chat", "RK": "", "Role": "Função", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "Direita para Esquerda", "Run": "Executar", "Running": "Executando", "Running...": "Executando...", "Save": "<PERSON><PERSON>", "Save & Create": "<PERSON>var e Criar", "Save & Update": "Salvar e Atualizar", "Save As Copy": "Salvar Como Cópia", "Save Chat": "<PERSON><PERSON>", "Save Tag": "<PERSON><PERSON>", "Saved": "Armazenado", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Salvar registros de chat diretamente no armazenamento do seu navegador não é mais suportado. Por favor, reserve um momento para baixar e excluir seus registros de chat clicando no botão abaixo. Não se preocupe, você pode facilmente reimportar seus registros de chat para o backend através de", "Scroll On Branch Change": "Rolar na mudança de ramo", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Search a model": "Pesquisar um modelo", "Search all emojis": "Pesquisar todos os emojis", "Search Base": "Pesquisar Base", "Search Chats": "<PERSON><PERSON><PERSON><PERSON>", "Search Collection": "Pesquisar <PERSON>", "Search Filters": "<PERSON><PERSON><PERSON><PERSON>", "search for archived chats": "pesquisar por chats arquivados", "search for folders": "procurar pastas", "search for pinned chats": "pesquisar por chats fixados", "search for shared chats": "procurar por chats compartilhados", "search for tags": "Pesquisar por tags", "Search Functions": "Pesquisar <PERSON>", "Search In Models": "Pesquisar em modelos", "Search Knowledge": "Pesquisar <PERSON>", "Search Models": "<PERSON><PERSON><PERSON><PERSON>", "Search Notes": "<PERSON><PERSON><PERSON><PERSON>", "Search options": "Opções de pesquisa", "Search Prompts": "Prompts de Pesquisa", "Search Result Count": "Contagem de Resultados da Pesquisa", "Search the internet": "Pesquisar na Internet", "Search Tools": "<PERSON><PERSON><PERSON><PERSON>", "SearchApi API Key": "Chave API SearchApi", "SearchApi Engine": "Motor SearchApi", "Searched {{count}} sites": "{{count}} sites pesquisados", "Searching": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Buscando conhecimento para \"{{searchQuery}}\"", "Searching the web": "Pesquisando na Internet...", "Searxng Query URL": "URL da Consulta Searxng", "See readme.md for instructions": "<PERSON><PERSON>a readme.md para instruções", "See what's new": "Veja o que há de novo", "Seed": "Seed", "Select": "Selecionar", "Select a base model": "Selecione um modelo base", "Select a base model (e.g. llama3, gpt-4o)": "Selecione um modelo básico (por exemplo, llama3, gpt-4o)", "Select a conversation to preview": "Selecione uma conversa para visualizar", "Select a engine": "Selecione um motor", "Select a function": "Selecione uma função", "Select a group": "Selecionar grupo", "Select a language": "Selecione um idioma", "Select a mode": "Selecione um modo", "Select a model": "Selecione um modelo", "Select a model (optional)": "Selecione um modelo (opcional)", "Select a pipeline": "Selecione um pipeline", "Select a pipeline url": "Selecione uma URL de pipeline", "Select a reranking model engine": "Selecione um mecanismo de modelo de reclassificação", "Select a role": "Selecione uma função", "Select a theme": "Selecione um tema", "Select a tool": "Selecione uma ferramenta", "Select a voice": "Selecione uma voz", "Select an auth method": "Selecione um método de autenticação", "Select an embedding model engine": "Selecione um mecanismo de modelo de embedding", "Select an engine": "Selecione um motor", "Select an Ollama instance": "Selecione uma instância do Ollama", "Select an output format": "Selecione um formato de saída", "Select dtype": "Selecionar dtype", "Select Engine": "Selecionar Motor", "Select how to split message text for TTS requests": "Selecione como dividir o texto da mensagem para solicitações TTS", "Select Knowledge": "Selecionar Conhecimento", "Select only one model to call": "Selecione apenas um modelo para chamar", "Select view": "", "Selected model(s) do not support image inputs": "Modelo(s) selecionado(s) não suportam entradas de imagem", "semantic": "semântica", "Send": "Enviar", "Send a Message": "Enviar uma Mensagem", "Send message": "Enviar mensagem", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Envia `stream_options: { include_usage: true }` na solicitação. Provedores compatíveis retornarão informações sobre o uso de tokens na resposta quando configurado.", "September": "Setembro", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "<PERSON><PERSON> da <PERSON>", "Serply API Key": "<PERSON><PERSON> da <PERSON>", "Serpstack API Key": "<PERSON><PERSON> da <PERSON>", "Server connection verified": "Conexão com o servidor verificada", "Session": "Sessão", "Set as default": "Definir como padrão", "Set CFG Scale": "Definir escala CFG", "Set Default Model": "Definir Modelo <PERSON>", "Set embedding model": "Definir modelo de embedding", "Set embedding model (e.g. {{model}})": "Definir modelo de embedding (por exemplo, {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Definir modelo de reclassificação (por exemplo, {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON>", "Set Scheduler": "Definir Agendador", "Set Steps": "Definir Etapas", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Defina o número de camadas que serão transferidas para a GPU. Aumentar esse valor pode melhorar significativamente o desempenho de modelos otimizados para aceleração de GPU, mas também pode consumir mais energia e recursos da GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Defina o número de threads de trabalho usadas para computação. Esta opção controla quantos threads são usados para processar as solicitações recebidas de forma simultânea. Aumentar esse valor pode melhorar o desempenho em cargas de trabalho de alta concorrência, mas também pode consumir mais recursos da CPU.", "Set Voice": "<PERSON><PERSON><PERSON>", "Set whisper model": "Definir modelo Whisper", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Define um viés fixo contra tokens que apareceram pelo menos uma vez. Um valor mais alto (por exemplo, 1,5) penalizará as repetições com mais força, enquanto um valor mais baixo (por exemplo, 0,9) será mais tolerante. Em 0, está desabilitado.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Define um viés de escala contra tokens para penalizar repetições, com base em quantas vezes elas apareceram. Um valor mais alto (por exemplo, 1,5) penalizará as repetições com mais rigor, enquanto um valor mais baixo (por exemplo, 0,9) será mais brando. Em 0, está desabilitado.", "Sets how far back for the model to look back to prevent repetition.": "Define até que ponto o modelo deve olhar para trás para evitar repetições.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Define a semente numérica aleatória a ser usada para geração. Definir um número específico fará com que o modelo gere o mesmo texto para o mesmo prompt.", "Sets the size of the context window used to generate the next token.": "Define o tamanho da janela de contexto usada para gerar o próximo token.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Define as sequências de parada a serem usadas. <PERSON>uando esse padrão for encontrado, o modelo de linguagem (LLM) parará de gerar texto e retornará. Vários padrões de parada podem ser definidos especificando parâmetros de parada separados em um arquivo de modelo.", "Settings": "Configurações", "Settings saved successfully!": "Configurações salvas com sucesso!", "Share": "Compartilhar", "Share Chat": "Comp<PERSON><PERSON><PERSON>", "Share to Open WebUI Community": "Compartilhar com a Comunidade OpenWebUI", "Share your background and interests": "Fale sobre você e seus interesses", "Shared with you": "", "Sharing Permissions": "Permissões de compartilhamento", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "Atalhos com um asterisco (*) são situacionais e só estão ativos em condições específicas.", "Show": "Mostrar", "Show \"What's New\" modal on login": "Mostrar \"O que há de Novo\" no login", "Show Admin Details in Account Pending Overlay": "Mostrar Detalhes do Administrador na Sobreposição de Conta Pendentes", "Show Formatting Toolbar": "Mostrar barra de ferramentas de formatação", "Show image preview": "Mostrar pré-visualização da imagem", "Show Model": "Mostrar modelo", "Show shortcuts": "<PERSON><PERSON> atalhos", "Show your support!": "Mostre seu apoio!", "Showcased creativity": "Criatividade exibida", "Sign in": "Entrar", "Sign in to {{WEBUI_NAME}}": "Faça login em {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Faça login em {{WEBUI_NAME}} com LDAP", "Sign Out": "<PERSON><PERSON>", "Sign up": "Inscrever-se", "Sign up to {{WEBUI_NAME}}": "Inscreva-se em {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "Melhora significativamente a precisão usando um LLM para aprimorar tabelas, formulários, cálculos em linha e detecção de layout. Aumenta a latência. O padrão é Falso.", "Signing in to {{WEBUI_NAME}}": "Fazendo login em {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "", "Skip Cache": "Pular cache", "Skip the cache and re-run the inference. Defaults to False.": "Ignore o cache e execute a inferência novamente. O padrão é Falso.", "Something went wrong :/": "Algo deu errado :/", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Fonte", "Speech Playback Speed": "Velocidade de reprodução de fala", "Speech recognition error: {{error}}": "Erro de reconhecimento de fala: {{error}}", "Speech-to-Text": "Fala-para-Texto", "Speech-to-Text Engine": "Motor de Transcrição de Fala", "standard": "", "Start of the channel": "Início do canal", "Start Tag": "Tag inicial", "Status Updates": "Atualizações de status", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "<PERSON><PERSON>", "Stop Generating": "<PERSON><PERSON> de gera<PERSON>", "Stop Sequence": "Sequência de Parada", "Stream Chat Response": "Stream Resposta do Chat", "Stream Delta Chunk Size": "", "Streamable HTTP": "", "Strikethrough": "<PERSON><PERSON><PERSON>", "Strip Existing OCR": "Remover OCR existente", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "Remove o texto OCR existente do PDF e executa o OCR novamente. Ignorado se a opção Forçar OCR estiver habilitada. O padrão é Falso.", "STT Model": "Modelo STT", "STT Settings": "Configurações STT", "Stylized PDF Export": "Exportação de PDF estilizado", "Subtitle (e.g. about the Roman Empire)": "Subtítulo (por exemplo, sobre o Império Romano)", "Success": "Sucesso", "Successfully imported {{userCount}} users.": "{{userCount}} usuários importados com sucesso.", "Successfully updated.": "Atualizado com sucesso.", "Suggest a change": "Su<PERSON><PERSON> uma mudan<PERSON>", "Suggested": "Sugerido", "Support": "Suporte", "Support this plugin:": "Apoie este plugin:", "Supported MIME Types": "Tipos MIME suportados", "Sync directory": "Sincronizar <PERSON>", "System": "Sistema", "System Instructions": "Instruções do sistema", "System Prompt": "Prompt do Sistema", "Table Mode": "<PERSON><PERSON>", "Tag": "", "Tags": "", "Tags Generation": "Geração de tags", "Tags Generation Prompt": "Prompt para geração de Tags", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "A amostragem livre de cauda é usada para reduzir o impacto de tokens menos prováveis na saída. Um valor mais alto (por exemplo, 2,0) reduzirá ainda mais o impacto, enquanto um valor de 1,0 desabilita essa configuração.", "Talk to model": "Fale com a modelo", "Tap to interrupt": "Toque para interromper", "Task List": "Lista de tarefas", "Task Model": "<PERSON><PERSON>", "Tasks": "<PERSON><PERSON><PERSON><PERSON>", "Tavily API Key": "<PERSON>ve da <PERSON>", "Tavily Extract Depth": "", "Tell us more:": "Conte-nos mais:", "Temperature": "Temperatura", "Temporary Chat": "Chat tempor<PERSON><PERSON>", "Temporary Chat by Default": "Chat tempor<PERSON>rio por padrão", "Text Splitter": "Divisor de Texto", "Text-to-Speech": "Texto-para-Fala", "Text-to-Speech Engine": "Motor de Texto para Fala", "Thanks for your feedback!": "<PERSON><PERSON><PERSON> pelo seu comentário!", "The Application Account DN you bind with for search": "O DN (Distinguished Name) da Conta de Aplicação com a qual você se conecta para pesquisa.", "The base to search for users": "Base para pesquisar usuários.", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "O tamanho do lote determina quantas solicitações de texto são processadas simultaneamente. Um tamanho de lote maior pode aumentar o desempenho e a velocidade do modelo, mas também requer mais memória.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Os desenvolvedores por trás deste plugin são voluntários apaixonados da comunidade. Se você achar este plugin útil, considere contribuir para o seu desenvolvimento.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "A evolução do ranking de avaliação é baseada no sistema Elo e será atualizada em tempo real.", "The format to return a response in. Format can be json or a JSON schema.": "O formato para retornar uma resposta. O formato pode ser json ou um esquema JSON.", "The height in pixels to compress images to. Leave empty for no compression.": "Altura em pixels para compactar as imagens. Deixe em branco para não compactar.", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "O idioma do áudio de entrada. Fornecer o idioma de entrada no formato ISO-639-1 (por exemplo, en) aumentará a precisão e a latência. Deixe em branco para detectar o idioma automaticamente.", "The LDAP attribute that maps to the mail that users use to sign in.": "O atributo LDAP que mapeia o e-mail que os usuários usam para fazer login.", "The LDAP attribute that maps to the username that users use to sign in.": "O atributo LDAP que mapeia para o nome de usuário que os usuários usam para fazer login.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "O ranking atual está em beta, e podemos ajustar as contas de avaliação como refinamos o algoritmo.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Máximo tamanho de arquivo em MB. Se o tamanho do arquivo exceder este limite, o arquivo não será enviado.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "O número máximo de arquivos que podem ser utilizados a cada vez em chat. Se o número de arquivos exceder este limite, os arquivos não serão enviados.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "Formato de saída para o texto. Pode ser 'json', 'markdown' ou 'html'. O padrão é 'markdown'.", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "A pontuação deve ser um valor entre 0.0 (0%) e 1.0 (100%).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "O tamanho do bloco delta do fluxo para o modelo. Aumentar o tamanho do bloco fará com que o modelo responda com trechos maiores de texto de uma só vez.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "A temperatura do modelo. Aumentar a temperatura fará com que o modelo responda de forma mais criativa.", "The Weight of BM25 Hybrid Search. 0 more semantic, 1 more lexical. Default 0.5": "O Peso da Busca Híbrida BM25. 0 a mais semântico, 1 a mais lexical. Padrão 0,5", "The width in pixels to compress images to. Leave empty for no compression.": "A largura em pixels para compactar as imagens. Deixe em branco para não compactar.", "Theme": "<PERSON><PERSON>", "Thinking...": "Pensando...", "This action cannot be undone. Do you wish to continue?": "Esta ação não pode ser desfeita. Você deseja continuar?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Este canal foi criado em {{createdAt}}. Este é o início do canal {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "Este chat não aparecerá no histórico e suas mensagens não serão salvas.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> garante que suas conversas valiosas sejam salvas com segurança no banco de dados do backend. Obrigado!", "This feature is experimental and may be modified or discontinued without notice.": "Este recurso é experimental e pode ser modificado ou descontinuado sem aviso prévio.", "This is a default user permission and will remain enabled.": "Esta é uma permissão de usuário padrão e permanecerá ativada.", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Esta é uma funcionalidade experimental, pode não funcionar como esperado e está sujeita a alterações a qualquer momento.", "This model is not publicly available. Please select another model.": "Este modelo não está disponível publicamente. Selecione outro modelo.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "Esta opção controla por quanto tempo o modelo permanecerá carregado na memória após a solicitação (padrão: 5m)", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Esta opção controla quantos tokens são preservados ao atualizar o contexto. <PERSON><PERSON> exemplo, se definido como 2, os últimos 2 tokens do contexto da conversa serão mantidos. Preservar o contexto pode ajudar a manter a continuidade de uma conversa, mas pode reduzir a capacidade de responder a novos tópicos.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "Esta opção habilita ou desabilita o uso do recurso de raciocínio no Ollama, que permite que o modelo pense antes de gerar uma resposta. Quando habilitada, o modelo pode levar um momento para processar o contexto da conversa e gerar uma resposta mais ponderada.", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Esta opção define o número máximo de tokens que o modelo pode gerar em sua resposta. Aumentar esse limite permite que o modelo forneça respostas mais longas, mas também pode aumentar a probabilidade de geração de conteúdo inútil ou irrelevante.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Essa opção deletará todos os arquivos existentes na coleção e todos eles serão substituídos.", "This response was generated by \"{{model}}\"": "Esta resposta foi gerada por \"{{model}}\"", "This will delete": "Is<PERSON> vai excluir", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Esta ação excluirá <strong>{{NAME}}</strong> e <strong>todos seus conteúdos</strong>.", "This will delete all models including custom models": "Isto vai excluir todos os modelos, incluindo personalizados", "This will delete all models including custom models and cannot be undone.": "Isto vai excluir todos os modelos, incluindo personalizados e não pode ser desfeito.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Esta ação resetará a base de conhecimento e sincronizará todos os arquivos. Deseja continuar?", "Thorough explanation": "Explicação detalhada", "Thought for {{DURATION}}": "Pensado por {{DURATION}}", "Thought for {{DURATION}} seconds": "Pensado por {{DURATION}} segundos", "Thought for less than a second": "Pensei por menos de um segundo", "Thread": "Tópico", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "URL do servidor Tika necessária.", "Tiktoken": "", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "T<PERSON><PERSON>lo (por exemplo, Conte-me um fato divertido)", "Title Auto-Generation": "Geração Automática de Título", "Title cannot be an empty string.": "O Título não pode ser uma string vazia.", "Title Generation": "Geração de Títulos", "Title Generation Prompt": "Prompt de Geração de Título", "TLS": "", "To access the available model names for downloading,": "Para acessar os nomes de modelos disponíveis para download,", "To access the GGUF models available for downloading,": "Para acessar os modelos GGUF disponíveis para download,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Para acessar a WebUI, entre em contato com o administrador. Os administradores podem gerenciar os status dos usuários no Painel de Administração.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Para anexar a base de conhecimento aqui, adicione-os ao espaço de trabalho \"Conhecimento\" primeiro.", "To learn more about available endpoints, visit our documentation.": "Para saber mais sobre os endpoints disponíveis, visite nossa documentação.", "To learn more about powerful prompt variables, click here": "Para saber mais sobre variáveis de prompt poderosas, clique aqui", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Para proteger sua privacidade, apenas classificações, IDs de modelo, tags e metadados são compartilhados a partir de seus comentários – seus registros de bate-papo permanecem privados e não são incluídos.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Para selecionar kits de ferramentas aqui, adicione-os ao espaço de trabalho \"Ferramentas\" primeiro.", "Toast notifications for new updates": "Notificações de alerta para novas atualizações", "Today": "Hoje", "Today at {{LOCALIZED_TIME}}": "Hoje às {{LOCALIZED_TIME}}", "Toggle search": "<PERSON>ern<PERSON> pesquisa", "Toggle settings": "Alternar configurações", "Toggle sidebar": "Alternar barra lateral", "Toggle whether current connection is active.": "Alterna se a conexão atual está ativa.", "Token": "", "Too verbose": "<PERSON><PERSON>", "Tool created successfully": "Ferramenta criada com sucesso", "Tool deleted successfully": "Ferramenta excluída com sucesso", "Tool Description": "Descrição da ferramenta", "Tool ID": "ID da ferramenta", "Tool imported successfully": "Ferramenta importada com sucesso", "Tool Name": "Nome da ferramenta", "Tool Servers": "Servid<PERSON> de ferramentas", "Tool updated successfully": "Ferramenta atualizada com sucesso", "Tools": "Ferramentas", "Tools Access": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "Tools are a function calling system with arbitrary code execution": "Ferramentas são um sistema de chamada de funções com execução de código arbitrário", "Tools Function Calling Prompt": "Prompt de chamada de função de ferramentas", "Tools have a function calling system that allows arbitrary code execution.": "Ferramentas possuem um sistema de chamada de funções que permite a execução de código arbitrário.", "Tools Public Sharing": "Ferramentas Compartilhamento Público", "Top K": "Top K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "Problemas para acessar o Ollama?", "Trust Proxy Environment": "Ambiente de Proxy Confiável", "Try adjusting your search or filter to find what you are looking for.": "", "Try Again": "Tentar novamente", "TTS Model": "Modelo TTS", "TTS Settings": "Configurações TTS", "TTS Voice": "Voz TTS", "Type": "Tipo", "Type here...": "Digite aqui...", "Type Hugging Face Resolve (Download) URL": "Digite o URL de download do Hugging Face", "Uh-oh! There was an issue with the response.": "Opa! Houve um problema com a resposta.", "UI": "Interface", "Unarchive All": "<PERSON>ar<PERSON><PERSON> tudo", "Unarchive All Archived Chats": "Desarquivar Todos os Chats Arquivados", "Unarchive Chat": "<PERSON><PERSON><PERSON><PERSON>", "Underline": "<PERSON><PERSON><PERSON><PERSON>", "Unknown": "Desconhecido", "Unknown User": "<PERSON><PERSON><PERSON><PERSON>", "Unloads {{FROM_NOW}}": "Descarrega {{FROM_NOW}}", "Unlock mysteries": "<PERSON><PERSON><PERSON>", "Unpin": "Desfixar", "Unravel secrets": "<PERSON><PERSON><PERSON>", "Unsupported file type.": "Tipo de arquivo não suportado.", "Untagged": "Sem tag", "Untitled": "<PERSON><PERSON> tí<PERSON>lo", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Atualizar e Copiar Link", "Update for the latest features and improvements.": "Atualizar para as novas funcionalidades e melhorias.", "Update password": "<PERSON><PERSON><PERSON><PERSON>", "Updated": "Atualizado", "Updated at": "Atualizado em", "Updated At": "Atualizado Em", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Faça upgrade para um plano licenciado para obter recursos aprimorados, incluindo temas e marcas personalizados e suporte dedicado.", "Upload": "Fazer upload", "Upload a GGUF model": "Fazer upload de um modelo GGUF", "Upload Audio": "<PERSON><PERSON><PERSON>", "Upload directory": "<PERSON><PERSON><PERSON>", "Upload files": "<PERSON><PERSON><PERSON>", "Upload Files": "<PERSON><PERSON><PERSON>", "Upload Model": "", "Upload Pipeline": "Fazer upload de Pipeline", "Upload Progress": "Progresso do Upload", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "Progresso do upload: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)", "URL": "", "URL is required": "URL é obrigratória", "URL Mode": "Modo URL", "Usage": "<PERSON><PERSON>", "Use '#' in the prompt input to load and include your knowledge.": "Usar '#' no prompt para carregar e incluir seus conhecimentos.", "Use groups to group your users and assign permissions.": "Use grupos para agrupar seus usuários e atribuir permissões.", "Use LLM": "Usar LLM", "Use no proxy to fetch page contents.": "Não utilize proxy para buscar o conteúdo da página.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Use o proxy designado pelas variáveis de ambiente http_proxy e https_proxy para buscar o conteúdo da página.", "user": "<PERSON><PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON>", "User Groups": "Grupos de usuários", "User location successfully retrieved.": "Localização do usuário recuperada com sucesso.", "User menu": "Menu do usuário", "User Webhooks": "Webhooks do usuário", "Username": "Nome do Usuário", "Users": "Usuários", "Uses DefaultAzureCredential to authenticate": "Usa DefaultAzureCredential para autenticar", "Uses OAuth 2.1 Dynamic Client Registration": "Utiliza o registro dinâmico de cliente OAuth 2.1", "Using Entire Document": "Usando o documento inteiro", "Using Focused Retrieval": "Usando Recuperação Focada", "Using the default arena model with all models. Click the plus button to add custom models.": "Usando a arena de modelos padrão para todos os modelos. Clique no botão mais para adicionar modelos personalizados.", "Valid time units:": "Unidades de tempo válidas:", "Validate certificate": "Validar certificado", "Valves": "Configurações", "Valves updated": "Configurações atualizadas", "Valves updated successfully": "Configurações atualizadas com sucesso", "variable": "<PERSON><PERSON><PERSON><PERSON>", "Verify Connection": "Verificar conexão", "Verify SSL Certificate": "Verificar certificado SSL", "Version": "Vers<PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Versão {{selectedVersion}} de {{totalVersions}}", "View Replies": "<PERSON>er respostas", "View Result from **{{NAME}}**": "Ver resultado de **{{NAME}}**", "Visibility": "Visibilidade", "Vision": "Visão", "vlm": "", "Voice": "Voz", "Voice Input": "Entrada de voz", "Voice mode": "<PERSON><PERSON> de <PERSON>oz", "Warning": "Aviso", "Warning:": "Aviso:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Aviso: Habilitar isso permitirá que os usuários façam upload de código arbitrário no servidor.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Aviso: Se você atualizar ou alterar seu modelo de incorporação, será necessário reimportar todos os documentos.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Aviso: a execução do Jupyter permite a execução de código arbitrário, o que representa sérios riscos de segurança. Prossiga com extremo cuidado.", "Web": "Web", "Web API": "API Web", "Web Loader Engine": "Motor de carregamento da Web", "Web Search": "Pesquisa na Web", "Web Search Engine": "Mecanismo de Busca na Web", "Web Search in Chat": "Pesquisa na Web no Chat", "Web Search Query Generation": "Geração de consulta de pesquisa na Web", "Webhook URL": "URL do Webhook", "Webpage URL": "URL da página da web", "WebUI Settings": "Configurações da WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "A WebUI fará requisições para \"{{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "A WebUI fará requisições para \"{{url}}/api/chat\".", "WebUI will make requests to \"{{url}}/chat/completions\"": "A WebUI fará requisições para \"{{url}}/chat/completions\".", "What are you trying to achieve?": "O que está tentando alcançar?", "What are you working on?": "No que está trabalhando?", "What's New in": "O que há de novo em", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON>uando habilitado, o modelo responderá a cada mensagem de chat em tempo real, gerando uma resposta assim que o usuário enviar uma mensagem. Este modo é útil para aplicativos de chat ao vivo, mas pode impactar o desempenho em hardware mais lento.", "wherever you are": "onde quer que você esteja.", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Se a saída deve ser paginada. Cada página será separada por uma régua horizontal e um número de página. O padrão é Falso.", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Local)", "Why?": "Por que?", "Widescreen Mode": "Modo Tela Cheia", "Width": "<PERSON><PERSON><PERSON>", "Won": "Ganhou", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Funciona em conjunto com o top-k. Um valor mais alto (por exemplo, 0,95) resultará em um texto mais diverso, enquanto um valor mais baixo (por exemplo, 0,5) gerará um texto mais focado e conservador.", "Workspace": "Espaço de Trabalho", "Workspace Permissions": "Permissões do espaço de trabalho", "Write": "Escrever", "Write a prompt suggestion (e.g. Who are you?)": "Escreva uma sugestão de prompt (por exemplo, Quem é você?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escreva um resumo em 50 palavras que resuma [tópico ou palavra-chave].", "Write something...": "Escreva algo...", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "Escreva aqui o conteúdo do system prompt do seu modelo\nex.: Você é o Mario do Super Mario Bros e atua como assistente.", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "Ontem", "Yesterday at {{LOCALIZED_TIME}}": "Ontem às {{LOCALIZED_TIME}}", "You": "Você", "You are currently using a trial license. Please contact support to upgrade your license.": "Você está usando uma licença de teste. Entre em contato com o suporte para atualizar sua licença.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Você só pode conversar com no máximo {{maxCount}} arquivo(s) de cada vez.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Você pode personalizar suas interações com LLMs adicionando memórias através do botão 'Gerenciar' abaixo, tornando-as mais <PERSON> e adaptadas a você.", "You cannot upload an empty file.": "Você não pode carregar um arquivo vazio.", "You do not have permission to send messages in this channel.": "Você não tem permissão para enviar mensagens neste canal.", "You do not have permission to send messages in this thread.": "Você não tem permissão para enviar mensagens neste tópico.", "You do not have permission to upload files.": "Você não tem permissão para fazer upload de arquivos.", "You have no archived conversations.": "Você não tem conversas arquivadas.", "You have shared this chat": "Você compartilhou este chat", "You're a helpful assistant.": "Você é um assistente útil.", "You're now logged in.": "Você agora está logado.", "Your Account": "Sua conta", "Your account status is currently pending activation.": "O status da sua conta está atualmente aguardando ativação.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Toda a sua contribuição irá diretamente para o desenvolvedor do plugin; o Open WebUI não retém nenhuma porcentagem. No entanto, a plataforma de financiamento escolhida pode ter suas próprias taxas.", "YouTube": "", "Youtube Language": "", "Youtube Proxy URL": ""}