{"-1 for no limit, or a positive integer for a specific limit": "-1 không gi<PERSON><PERSON> hạn, hoặc một số nguyên dương cho giới hạn cụ thể", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' hoặc '-1' không hết hạn.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(vd: `sh webui.sh --api --api-auth tên_người_dùng_mật_khẩu`)", "(e.g. `sh webui.sh --api`)": "(vd: `sh webui.sh --api`)", "(latest)": "(mới nhất)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ mô hình }}", "{{COUNT}} Available Tools": "{{COUNT}} <PERSON><PERSON>ng cụ có sẵn", "{{COUNT}} characters": "", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "{{COUNT}} dòng b<PERSON>n", "{{COUNT}} Replies": "{{COUNT}} <PERSON><PERSON><PERSON> lời", "{{COUNT}} Sources": "", "{{COUNT}} words": "", "{{LOCALIZED_DATE}} at {{LOCALIZED_TIME}}": "", "{{model}} download has been canceled": "", "{{user}}'s Chats": "<PERSON><PERSON><PERSON>c trò chuy<PERSON>n của {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} <PERSON><PERSON><PERSON> c<PERSON><PERSON>end", "*Prompt node ID(s) are required for image generation": "*ID nút Prompt là bắt buộc để tạo <PERSON>nh", "1 Source": "", "A new version (v{{LATEST_VERSION}}) is now available.": "<PERSON><PERSON><PERSON> phiên bản mới (v{{LATEST_VERSION}}) đã có sẵn.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "<PERSON><PERSON> hình tác vụ được sử dụng khi thực hiện các tác vụ như tạo tiêu đề cho cuộc trò chuyện và truy vấn tìm kiếm trên web", "a user": "người sử dụng", "About": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "Accept autocomplete generation / Jump to prompt variable": "<PERSON><PERSON><PERSON> nhận tạo tự động hoàn thành / Chuyển đến biến prompt", "Access": "<PERSON><PERSON><PERSON><PERSON>", "Access Control": "<PERSON><PERSON><PERSON> so<PERSON> t<PERSON>y c<PERSON>p", "Accessible to all users": "<PERSON><PERSON><PERSON> cậ<PERSON> đ<PERSON> bởi tất cả người dùng", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "<PERSON><PERSON><PERSON> k<PERSON>n đang chờ kích ho<PERSON>t", "accurate": "", "Accurate information": "Thông tin chính xác", "Action": "", "Action not found": "", "Action Required for Chat Log Storage": "<PERSON><PERSON><PERSON> thao tác để lưu nhật ký trò chuyện", "Actions": "Tác vụ", "Activate": "<PERSON><PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "<PERSON><PERSON><PERSON> ho<PERSON>t lệnh này bằng cách gõ \"/{{COMMAND}}\" vào ô nhập chat.", "Active": "<PERSON><PERSON><PERSON> đ<PERSON>", "Active Users": "Người dùng đang hoạt động", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Thêm ID mô hình", "Add a short description about what this model does": "<PERSON><PERSON><PERSON><PERSON> mô tả ngắn về những khả năng của model", "Add a tag": "Thêm thẻ (tag)", "Add Arena Model": "<PERSON><PERSON><PERSON><PERSON>", "Add Connection": "<PERSON><PERSON><PERSON><PERSON> kế<PERSON>", "Add Content": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i dung", "Add content here": "<PERSON><PERSON><PERSON><PERSON> nội dung tại đây", "Add Custom Parameter": "", "Add custom prompt": "Thêm prompt tùy chỉnh", "Add Details": "", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "<PERSON><PERSON><PERSON><PERSON>", "Add Memory": "<PERSON><PERSON><PERSON><PERSON> bộ nhớ", "Add Model": "Thêm model", "Add Reaction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Tag": "Thê<PERSON> thẻ", "Add Tags": "thêm thẻ", "Add text content": "<PERSON><PERSON><PERSON><PERSON> nội dung văn bản", "Add User": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> dùng", "Add User Group": "<PERSON><PERSON><PERSON><PERSON> dùng", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Additional Parameters": "", "Adjusting these settings will apply changes universally to all users.": "<PERSON><PERSON><PERSON> thay đổi cài đặt này sẽ áp dụng cho tất cả người sử dụng.", "admin": "quản trị viên", "Admin": "<PERSON><PERSON><PERSON><PERSON> trị", "Admin Panel": "<PERSON><PERSON> trị", "Admin Settings": "<PERSON><PERSON><PERSON> đặt hệ thống", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Quản trị viên luôn có quyền truy cập vào tất cả các tool; người dùng cần các tools được chỉ định cho mỗi mô hình trong workspace.", "Advanced Parameters": "<PERSON><PERSON><PERSON> tham số <PERSON> cao", "Advanced Params": "<PERSON><PERSON><PERSON> tham số <PERSON> cao", "AI": "", "All": "<PERSON><PERSON><PERSON> c<PERSON>", "All chats have been unarchived.": "", "All Documents": "<PERSON><PERSON><PERSON> cả tài liệu", "All models deleted successfully": "Tất cả các mô hình đã được xóa thành công", "Allow Call": "", "Allow Chat Controls": "<PERSON> phép <PERSON>", "Allow Chat Delete": "Cho phép <PERSON>", "Allow Chat Deletion": "<PERSON> phép <PERSON> nội dung chat", "Allow Chat Edit": "<PERSON> phép Chỉnh sửa <PERSON>t", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "<PERSON> phép <PERSON><PERSON> tệp lên", "Allow Multiple Models in Chat": "", "Allow non-local voices": "<PERSON> phép giọng nói không bản xứ", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "", "Allow Temporary Chat": "<PERSON> phép <PERSON> nh<PERSON>p", "Allow Text to Speech": "", "Allow User Location": "<PERSON> phép sử dụng vị trí người dùng", "Allow Voice Interruption in Call": "<PERSON> phép gián đoạn giọng nói trong cuộc gọi", "Allowed Endpoints": "<PERSON><PERSON><PERSON> Endpoint đ<PERSON><PERSON><PERSON> ph<PERSON>p", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Bạn đã có tài k<PERSON>n?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Thay thế cho top_p, nhằm đảm bảo cân bằng giữa chất lượng và sự đa dạng. Tham số p đại diện cho xác suất tối thiểu để một token được xem xét, tương đối so với xác suất của token có khả năng cao nhất. Ví dụ: với p=0.05 và token có khả năng cao nhất có xác suất 0.9, các logit có giá trị nhỏ hơn 0.045 sẽ bị lọc ra.", "Always": "<PERSON><PERSON><PERSON> luôn", "Always Collapse Code Blocks": "<PERSON><PERSON><PERSON> gọn <PERSON> mã", "Always Expand Details": "Luôn Mở rộng Chi tiết", "Always Play Notification Sound": "", "Amazing": "Tuyệt vời", "an assistant": "tr<PERSON> lý", "An error occurred while fetching the explanation": "", "Analytics": "", "Analyzed": "Đã phân tích", "Analyzing...": "Đang phân tích...", "and": "và", "and {{COUNT}} more": "và {{COUNT}} mục kh<PERSON>c", "and create a new shared link.": "và tạo một link chia sẻ mới", "Android": "", "API": "", "API Base URL": "Đường dẫn tới API (API Base URL)", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API Key", "API Key created.": "Khóa API đã tạo", "API Key Endpoint Restrictions": "Hạn chế Endpoint Khóa API", "API keys": "API Keys", "API Version": "", "API Version is required": "", "Application DN": "DN Ứng dụng", "Application DN Password": "<PERSON><PERSON><PERSON> khẩu DN Ứng dụng", "applies to all users with the \"user\" role": "áp dụng cho tất cả người dùng có vai trò \"user\"", "April": "Tháng 4", "Archive": "<PERSON><PERSON><PERSON> tr<PERSON>", "Archive All Chats": "<PERSON><PERSON><PERSON> tất cả c<PERSON>c <PERSON>", "Archived Chats": "<PERSON><PERSON><PERSON>", "archived-chat-export": "xuất-chat-lưu-trữ", "Are you sure you want to clear all memories? This action cannot be undone.": "Bạn có chắc chắn muốn xóa tất cả bộ nhớ không? Hành động này không thể hoàn tác.", "Are you sure you want to delete this channel?": "Bạn có chắc chắn muốn xóa kênh này không?", "Are you sure you want to delete this message?": "Bạn có chắc chắn muốn xóa tin nhắn này không?", "Are you sure you want to unarchive all archived chats?": "Bạn có chắc chắn muốn bỏ lưu trữ tất cả các cuộc trò chuyện đã lưu trữ không?", "Are you sure?": "Bạn có chắc chắn không?", "Arena Models": "<PERSON><PERSON><PERSON>", "Artifacts": "<PERSON><PERSON><PERSON> quả tạo ra", "Ask": "Hỏi", "Ask a question": "Đặt câu hỏi", "Assistant": "<PERSON><PERSON><PERSON>", "Attach file from knowledge": "<PERSON><PERSON><PERSON> kèm tệp từ kiến thức", "Attach Knowledge": "", "Attach Notes": "", "Attach Webpage": "", "Attention to detail": "<PERSON><PERSON> sự chú ý đến chi tiết của vấn đề", "Attribute for Mail": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> cho <PERSON>", "Attribute for Username": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h cho Tên người dùng", "Audio": "<PERSON><PERSON>", "August": "Tháng 8", "Auth": "<PERSON><PERSON><PERSON> th<PERSON>c", "Authenticate": "<PERSON><PERSON><PERSON> th<PERSON>c", "Authentication": "<PERSON><PERSON><PERSON> th<PERSON>c", "Auto": "", "Auto-Copy Response to Clipboard": "Tự động <PERSON> ch<PERSON>p <PERSON> hồi vào clipboard", "Auto-playback response": "<PERSON><PERSON> động phát lại phản hồi (Auto-playback)", "Autocomplete Generation": "<PERSON><PERSON>o Tự động <PERSON>n thành", "Autocomplete Generation Input Max Length": "<PERSON>ộ dài tối đa đầu vào Tạo Tự động Hoàn thành", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Chuỗi xác thực API AUTOMATIC1111", "AUTOMATIC1111 Base URL": "Đường dẫn kết nối tới AUTOMATIC1111 (Base URL)", "AUTOMATIC1111 Base URL is required.": "Base URL của AUTOMATIC1111 là bắt buộc.", "Available list": "<PERSON><PERSON> s<PERSON>ch có sẵn", "Available Tools": "<PERSON><PERSON>ng cụ có sẵn", "available users": "người dùng khả dụng", "available!": "có sẵn!", "Away": "Vắng mặt", "Awful": "<PERSON>ệ", "Azure AI Speech": "Azure AI Speech", "Azure OpenAI": "Azure OpenAI", "Azure Region": "<PERSON><PERSON> vực Azure", "Back": "Quay lại", "Bad Response": "<PERSON><PERSON><PERSON> lời KHÔNG tốt", "Banners": "<PERSON><PERSON><PERSON><PERSON>", "Base Model (From)": "<PERSON><PERSON> hình cơ sở (từ)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "Bearer": "", "before": "tr<PERSON><PERSON><PERSON>", "Being lazy": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON>", "Beta": "Beta", "Bing Search V7 Endpoint": "Endpoint Bing Search V7", "Bing Search V7 Subscription Key": "Khóa đăng ký Bing Search V7", "Bio": "", "Birth Date": "", "BM25 Weight": "", "Bocha Search API Key": "Khóa API Bocha Search", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Tăng cường hoặc phạt các token cụ thể cho các phản hồi bị ràng buộc. Gi<PERSON> trị bias sẽ được giới hạn trong khoảng từ -100 đến 100 (bao gồ<PERSON>). (Mặc định: không có)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Khóa API tìm kiếm dũng cảm", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "Bởi {{name}}", "Bypass Embedding and Retrieval": "Bỏ qua Embedding v<PERSON> Tru<PERSON> xu<PERSON>t", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "Gọi", "Call feature is not supported when using Web STT engine": "<PERSON><PERSON><PERSON> năng gọi điện không được hỗ trợ khi sử dụng công cụ Web STT", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Hủy bỏ", "Capabilities": "<PERSON><PERSON><PERSON> l<PERSON>", "Capture": "<PERSON><PERSON><PERSON>", "Capture Audio": "", "Certificate Path": "Đường dẫn Chứng chỉ", "Change Password": "<PERSON><PERSON><PERSON>", "Channel": "", "Channel deleted successfully": "", "Channel Name": "<PERSON><PERSON><PERSON>", "Channel updated successfully": "", "Channels": "<PERSON><PERSON><PERSON>", "Character": "Nhân vật", "Character limit for autocomplete generation input": "G<PERSON><PERSON>i hạn ký tự cho đầu vào tạo tự động hoàn thành", "Chart new frontiers": "Vẽ nên những giới hạn mới", "Chat": "<PERSON><PERSON><PERSON>", "Chat Background Image": "<PERSON><PERSON><PERSON>n trò chuy<PERSON>n", "Chat Bubble UI": "<PERSON><PERSON><PERSON> chat", "Chat Controls": "<PERSON><PERSON><PERSON><PERSON>", "Chat Conversation": "", "Chat direction": "<PERSON><PERSON><PERSON>ng chat", "Chat ID": "", "Chat moved successfully": "", "Chat Overview": "<PERSON><PERSON><PERSON> quan <PERSON>", "Chat Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Chat Tags Auto-Generation": "Tự động tạo Thẻ Chat", "Chats": "Cha<PERSON>", "Check Again": "<PERSON><PERSON><PERSON>", "Check for updates": "<PERSON><PERSON><PERSON> tra cập nh<PERSON>t", "Checking for updates...": "<PERSON><PERSON> kiểm tra cập nhật...", "Choose a model before saving...": "<PERSON><PERSON><PERSON> mô hình trư<PERSON>c khi lưu...", "Chunk Overlap": "<PERSON><PERSON><PERSON> lấn (overlap)", "Chunk Size": "<PERSON><PERSON><PERSON> (size)", "Ciphers": "Bộ mã hóa", "Citation": "<PERSON><PERSON><PERSON><PERSON> dẫn", "Citations": "", "Clear memory": "Xóa bộ nhớ", "Clear Memory": "Xóa Bộ nhớ", "click here": "nhấn vào đây", "Click here for filter guides.": "Nhấn vào đây để xem hướng dẫn về bộ lọc.", "Click here for help.": "<PERSON><PERSON>m vào đây để được trợ giúp.", "Click here to": "Nhấn vào đây để", "Click here to download user import template file.": "<PERSON>ấm vào đây để tải xuống tệp template của người dùng.", "Click here to learn more about faster-whisper and see the available models.": "Nhấn vào đây để tìm hiểu thêm về faster-whisper và xem các mô hình có sẵn.", "Click here to see available models.": "Nhấn vào đây để xem các mô hình có sẵn.", "Click here to select": "<PERSON>ấm vào đây để chọn", "Click here to select a csv file.": "<PERSON>hấn vào đây để chọn tệp csv", "Click here to select a py file.": "<PERSON><PERSON>ấn vào đây để chọn tệp py", "Click here to upload a workflow.json file.": "Bấm vào đ<PERSON>y để upload file worklow.json", "click here.": "bấm vào đây.", "Click on the user role button to change a user's role.": "Bấm vào nút trong cột VAI TRÒ để thay đổi quyền của người sử dụng.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Quyền ghi vào clipboard bị từ chối. Vui lòng kiểm tra cài đặt trên trình duyệt của bạn để được cấp quyền truy cập cần thiết.", "Clone": "<PERSON><PERSON><PERSON> b<PERSON>n", "Clone Chat": "<PERSON><PERSON><PERSON> b<PERSON>", "Clone of {{TITLE}}": "<PERSON><PERSON><PERSON> sao của {{TITLE}}", "Close": "Đ<PERSON><PERSON>", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "CMU ARCTIC speaker embedding name": "", "Code Block": "", "Code execution": "<PERSON>h<PERSON>c thi mã", "Code Execution": "<PERSON>h<PERSON><PERSON> thi <PERSON>ã", "Code Execution Engine": "Engine Thực thi Mã", "Code Execution Timeout": "Th<PERSON>i gian chờ Thực thi Mã", "Code formatted successfully": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> định dạng thành công", "Code Interpreter": "<PERSON><PERSON><PERSON><PERSON> thông dịch <PERSON>", "Code Interpreter Engine": "Engine Trì<PERSON> thông dịch <PERSON>", "Code Interpreter Prompt Template": "Mẫu Prompt <PERSON><PERSON><PERSON><PERSON> thông dịch <PERSON>", "Collapse": "<PERSON><PERSON>", "Collection": "<PERSON><PERSON><PERSON> hợp mọi tài li<PERSON>u", "Color": "<PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Khóa API ComfyUI", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "Base URL của ComfyUI là bắt buộc.", "ComfyUI Workflow": "<PERSON><PERSON> trình làm việc ComfyUI", "ComfyUI Workflow Nodes": "<PERSON><PERSON><PERSON>uy trình làm việc ComfyUI", "Comma separated Node Ids (e.g. 1 or 1,2)": "", "Command": "<PERSON><PERSON><PERSON>", "Comment": "", "Completions": "<PERSON><PERSON><PERSON> th<PERSON>", "Compress Images in Channels": "", "Concurrent Requests": "<PERSON><PERSON><PERSON> truy vấn đồng thời", "Config imported successfully": "", "Configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "Confirm": "<PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON><PERSON><PERSON>", "Confirm your action": "<PERSON><PERSON><PERSON>ận hành động của bạn", "Confirm your new password": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới của bạn", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "<PERSON>ết nối với các điểm cuối API tương thích OpenAI của riêng bạn.", "Connect to your own OpenAPI compatible external tool servers.": "<PERSON>ết n<PERSON><PERSON> với các máy chủ công cụ bên ngoài tương thích OpenAPI của riêng bạn.", "Connection failed": "<PERSON><PERSON><PERSON> n<PERSON>i thất bại", "Connection successful": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "Connection Type": "", "Connections": "<PERSON><PERSON><PERSON>", "Connections saved successfully": "<PERSON><PERSON> lưu kết nối thành công", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Hạn chế nỗ lực suy luận cho các mô hình suy luận. Chỉ áp dụng cho các mô hình suy luận từ các nhà cung cấp cụ thể hỗ trợ nỗ lực suy luận.", "Contact Admin for WebUI Access": "<PERSON><PERSON><PERSON> hệ với <PERSON>ản trị viên để đư<PERSON><PERSON> cấp quyền truy cập", "Content": "<PERSON><PERSON>i dung", "Content Extraction Engine": "Engine Trích xu<PERSON>t <PERSON> dung", "Continue Response": "<PERSON><PERSON><PERSON><PERSON> tục trả lời", "Continue with {{provider}}": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> với {{provider}}", "Continue with Email": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> v<PERSON><PERSON>", "Continue with LDAP": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> với LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON><PERSON> soát cách văn bản tin nhắn được chia nhỏ cho các yêu cầu TTS. 'Dấu câu' chia thành câu, 'đoạn văn' chia thành đoạn văn và 'không' giữ tin nhắn thành một chuỗi duy nhất.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "<PERSON><PERSON><PERSON> so<PERSON>t sự lặp lại của các chuỗi token trong văn bản được tạo. <PERSON><PERSON><PERSON> trị cao hơn (ví dụ: 1.5) sẽ phạt sự lặp lại mạnh hơn, trong khi giá trị thấp hơn (ví dụ: 1.1) sẽ khoan dung hơn. Tại 1, nó bị vô hiệu hóa.", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "<PERSON><PERSON><PERSON> so<PERSON>t sự cân bằng giữa tính mạch lạc và sự đa dạng của đầu ra. Gi<PERSON> trị thấp hơn sẽ dẫn đến văn bản tập trung và mạch lạc hơn.", "Conversation saved successfully": "", "Copied": "Đã sao chép", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Đã sao chép link chia sẻ trò chuyện vào clipboard!", "Copied to clipboard": "Đã sao chép vào clipboard", "Copy": "Sao chép", "Copy Formatted Text": "", "Copy last code block": "<PERSON>o chép khối mã cuối cùng", "Copy last response": "<PERSON><PERSON> ch<PERSON><PERSON> ph<PERSON>n hồi cuối cùng", "Copy link": "", "Copy Link": "Sao chép link", "Copy to clipboard": "Sao chép vào clipboard", "Copying to clipboard was successful!": "Sao chép vào clipboard thành công!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS phải được cấu hình đúng bởi nhà cung cấp để cho phép các yêu cầu từ Open WebUI.", "Create": "Tạo", "Create a knowledge base": "<PERSON><PERSON><PERSON> cơ sở kiến thức", "Create a model": "Tạo model", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "<PERSON><PERSON><PERSON>n trị", "Create Channel": "<PERSON><PERSON><PERSON>", "Create Folder": "", "Create Group": "Tạo Nhó<PERSON>", "Create Knowledge": "<PERSON><PERSON><PERSON> thức", "Create Model": "", "Create new key": "Tạo key mới", "Create new secret key": "Tạo key bí mật mới", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "<PERSON><PERSON><PERSON><PERSON> tạo vào lúc", "Created At": "Tạo lúc", "Created by": "Tạo bởi", "Created by you": "", "CSV Import": "Nạp CSV", "Ctrl+Enter to Send": "Ctrl+Enter đ<PERSON>", "Current Model": "<PERSON><PERSON> hình hiện tại", "Current Password": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "Custom": "<PERSON><PERSON><PERSON> chỉnh", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "<PERSON><PERSON><PERSON>", "Dark": "<PERSON><PERSON><PERSON>", "Data Controls": "", "Database": "C<PERSON> sở dữ liệu", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Tháng 12", "Deepgram": "", "Default": "Mặc định", "Default (Open AI)": "Mặc đ<PERSON>nh (Open AI)", "Default (SentenceTransformers)": "Mặc định (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default Features": "", "Default Filters": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Chế độ mặc định hoạt động với nhiều loại mô hình hơn bằng cách gọi các công cụ một lần trước khi thực thi. Chế độ gốc tận dụng khả năng gọi công cụ tích hợp sẵn của mô hình, nhưng yêu cầu mô hình phải hỗ trợ tính năng này vốn có.", "Default Model": "<PERSON> mặc định", "Default model updated": "<PERSON><PERSON> hình mặc định đã đư<PERSON><PERSON> cập nhật", "Default Models": "<PERSON><PERSON><PERSON> hình Mặc định", "Default permissions": "Quyền mặc định", "Default permissions updated successfully": "<PERSON><PERSON> cập nhật quyền mặc định thành công", "Default Prompt Suggestions": "<PERSON><PERSON> xuất prompt mặc định", "Default to 389 or 636 if TLS is enabled": "Mặc định là 389 hoặc 636 nếu T<PERSON> đ<PERSON><PERSON><PERSON> bật", "Default to ALL": "Mặc định là TẤT CẢ", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Mặc định truy xuất phân đoạn để trích xuất nội dung tập trung và phù hợp, điều nà<PERSON> đư<PERSON><PERSON> khu<PERSON>ến nghị cho hầu hết các trường hợp.", "Default User Role": "<PERSON>ai trò mặc định", "Delete": "Xóa", "Delete a model": "<PERSON><PERSON><PERSON> mô hình", "Delete All Chats": "<PERSON><PERSON><PERSON>", "Delete All Models": "<PERSON><PERSON><PERSON> cả <PERSON> hình", "Delete chat": "<PERSON><PERSON><PERSON> n<PERSON>i dung chat", "Delete Chat": "Xóa chat", "Delete chat?": "Xóa chat?", "Delete folder?": "<PERSON><PERSON><PERSON> thư mục?", "Delete function?": "Xóa function?", "Delete Message": "<PERSON><PERSON><PERSON>", "Delete message?": "<PERSON><PERSON><PERSON> tin nhắn?", "Delete Model": "", "Delete note?": "", "Delete prompt?": "Xóa prompt?", "delete this link": "Xóa link này", "Delete tool?": "Xóa tool?", "Delete User": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "Deleted {{deleteModelTag}}": "Đ<PERSON> xóa {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON> xóa {{name}}", "Deleted User": "Người dùng đã xóa", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "<PERSON><PERSON> tả cơ sở kiến thức và mục tiêu của bạn", "Description": "<PERSON><PERSON>", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "<PERSON><PERSON><PERSON><PERSON> tuân theo chỉ dẫn một cách đầy đủ", "Direct": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Direct Connections": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Kết nối Trực tiếp cho phép người dùng kết nối với các điểm cuối API tương thích OpenAI của riêng họ.", "Direct Tool Servers": "<PERSON><PERSON><PERSON> chủ <PERSON>ng cụ Trực tiếp", "Directory selection was cancelled": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Đã tắt", "Discover a function": "Khám phá function", "Discover a model": "<PERSON><PERSON><PERSON><PERSON> phá model", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON> phá thêm prompt mới", "Discover a tool": "Khám phá tool", "Discover how to use Open WebUI and seek support from the community.": "<PERSON><PERSON><PERSON><PERSON> phá cách sử dụng Open WebUI và tìm kiếm sự hỗ trợ từ cộng đồng.", "Discover wonders": "<PERSON>h<PERSON><PERSON> phá những điều kỳ diệu", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON> k<PERSON>, tả<PERSON> về và khám phá thêm các function tùy chỉnh", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, tả<PERSON> về và khám phá thêm các prompt tùy chỉnh", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON>, tả<PERSON> về và khám phá thêm các tool tùy chỉnh", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON>, t<PERSON><PERSON> về và khám phá thêm các model presets", "Display": "<PERSON><PERSON><PERSON> thị", "Display chat title in tab": "", "Display Emoji in Call": "Hi<PERSON>n thị Emoji trong cuộc g<PERSON>i", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "<PERSON><PERSON>n thị tên người sử dụng thay vì 'Bạn' trong nội dung chat", "Displays citations in the response": "<PERSON><PERSON><PERSON> thị trích dẫn trong phản hồi", "Displays status updates (e.g., web search progress) in the response": "", "Dive into knowledge": "<PERSON><PERSON> sâu vào kiến thức", "dlparse_v1": "", "dlparse_v2": "", "dlparse_v4": "", "Do not install functions from sources you do not fully trust.": "Không cài đặt các functions từ các nguồn mà bạn không hoàn toàn tin tưởng.", "Do not install tools from sources you do not fully trust.": "Không cài đặt các tools từ những nguồn mà bạn không hoàn toàn tin tưởng.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "<PERSON><PERSON><PERSON> c<PERSON>u URL Máy chủ <PERSON>ling.", "Document": "<PERSON><PERSON><PERSON> l<PERSON>", "Document Intelligence": "<PERSON>r<PERSON> tuệ Tài liệu", "Document Intelligence endpoint required.": "", "Documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "Documents": "<PERSON><PERSON><PERSON> l<PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "<PERSON><PERSON><PERSON><PERSON> thực hiện bất kỳ kết nối ngoài nào, và dữ liệu của bạn vẫn được lưu trữ an toàn trên máy chủ lưu trữ cục bộ của bạn.", "Domain Filter List": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "don't fetch random pipelines from sources you don't trust.": "Đừng lấy các pipelines ngẫu nhiên từ nguồn không đáng tin cậy.", "Don't have an account?": "<PERSON><PERSON><PERSON>ng có tài k<PERSON>n?", "don't install random functions from sources you don't trust.": "không cài đặt các function từ các nguồn mà bạn không tin tưởng.", "don't install random tools from sources you don't trust.": "không cài đặt các tools từ các nguồn mà bạn không tin tưởng.", "Don't like the style": "<PERSON><PERSON><PERSON><PERSON> thích phong cách trả lời", "Done": "<PERSON><PERSON><PERSON> th<PERSON>", "Download": "<PERSON><PERSON><PERSON> về", "Download & Delete": "<PERSON>ải xuống và xóa", "Download as SVG": "<PERSON><PERSON><PERSON> xuống dưới dạng SVG", "Download canceled": "Đã hủy download", "Download Database": "<PERSON><PERSON><PERSON> xuống <PERSON> sở dữ liệu", "Drag and drop a file to upload or select a file to view": "<PERSON><PERSON><PERSON> và thả tệp để tải lên hoặc chọn tệp để xem", "Draw": "Vẽ", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "vd: '30s','10m'. Đơn vị thời gian hợp lệ là 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "ví dụ: \"json\" hoặc một lược đồ JSON", "e.g. 60": "vd: 60", "e.g. A filter to remove profanity from text": "vd: <PERSON><PERSON> lọc để loại bỏ từ ngữ tục tĩu khỏi văn bản", "e.g. en": "", "e.g. My Filter": "vd: <PERSON><PERSON> c<PERSON>a tôi", "e.g. My Tools": "vd: <PERSON><PERSON><PERSON> c<PERSON> của tôi", "e.g. my_filter": "vd: bo_loc_cua_toi", "e.g. my_tools": "vd: cong_cu_cua_toi", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "vd: <PERSON><PERSON><PERSON> công cụ để thực hiện các hoạt động khác nhau", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "Chỉnh sửa", "Edit Arena Model": "Chỉnh sửa <PERSON> h<PERSON>nh <PERSON>", "Edit Channel": "Chỉnh s<PERSON><PERSON>", "Edit Connection": "Chỉnh s<PERSON>a <PERSON>i", "Edit Default Permissions": "Chỉnh s<PERSON><PERSON>ền Mặc định", "Edit Folder": "", "Edit Memory": "S<PERSON>a Memory", "Edit User": "Thay đổi thông tin người sử dụng", "Edit User Group": "Chỉnh s<PERSON>a <PERSON> dùng", "edited": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "<PERSON><PERSON><PERSON> đầu nh<PERSON>ng cuộc phiêu lưu", "Embedding": "Embedding", "Embedding Batch Size": "<PERSON><PERSON><PERSON><PERSON> Embedding", "Embedding Model": "<PERSON><PERSON> h<PERSON>nh embedding", "Embedding Model Engine": "<PERSON><PERSON><PERSON><PERSON> lý embedding", "Embedding model set to \"{{embedding_model}}\"": "<PERSON><PERSON> hình embedding đ<PERSON> đ<PERSON><PERSON><PERSON> thiết lập thành \"{{embedding_model}}\"", "Enable API Key": "Bật Khóa API", "Enable autocomplete generation for chat messages": "<PERSON><PERSON>t tạo tự động hoàn thành cho tin nhắn chat", "Enable Code Execution": "<PERSON><PERSON><PERSON> thi <PERSON>", "Enable Code Interpreter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> thông dịch <PERSON>", "Enable Community Sharing": "<PERSON> phép Chia sẻ Cộng đồng", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Bật Kh<PERSON>a Bộ nhớ (mlock) để ngăn dữ liệu mô hình bị hoán đổi ra khỏi RAM. Tùy chọn này khóa tập trang làm việc của mô hình vào RAM, đả<PERSON> bảo rằng chúng sẽ không bị hoán đổi ra đĩa. Điều này có thể giúp duy trì hiệu suất bằng cách tránh lỗi trang và đảm bảo truy cập dữ liệu nhanh chóng.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Bật Ánh xạ Bộ nhớ (mmap) để tải dữ liệu mô hình. T<PERSON><PERSON> chọn này cho phép hệ thống sử dụng bộ nhớ đĩa như một phần mở rộng của RAM bằng cách coi các tệp đĩa như thể chúng ở trong RAM. Điều này có thể cải thiện hiệu suất mô hình bằng cách cho phép truy cập dữ liệu nhanh hơn. <PERSON><PERSON>, nó có thể không hoạt động chính xác với tất cả các hệ thống và có thể tiêu tốn một lượng đáng kể dung lượng đĩa.", "Enable Message Rating": "<PERSON> phép p<PERSON><PERSON><PERSON> hồ<PERSON>, đ<PERSON><PERSON> giá", "Enable Mirostat sampling for controlling perplexity.": "<PERSON><PERSON><PERSON> lấy mẫu Mirostat để kiểm soát perplexity.", "Enable New Sign Ups": "<PERSON> phép đăng ký mới", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "<PERSON><PERSON> bật", "End Tag": "", "Endpoint URL": "", "Enforce Temporary Chat": "<PERSON><PERSON><PERSON> b<PERSON>", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON> bảo tệp CSV của bạn bao gồm 4 cột theo thứ tự sau: Name, Email, Password, Role.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON><PERSON> yêu cầu của {{role}} ở đây", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON><PERSON> chi tiết về bản thân của bạn để LLMs có thể nhớ", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter additional headers in JSON format": "", "Enter additional headers in JSON format (e.g. {{'{{\"X-Custom-Header\": \"value\"}}'}})": "", "Enter additional parameters in JSON format": "", "Enter api auth string (e.g. username:password)": "<PERSON><PERSON><PERSON><PERSON> chuỗi xác thực api (ví dụ: username: mật khẩu)", "Enter Application DN": "Nhập DN Ứng dụng", "Enter Application DN Password": "<PERSON><PERSON><PERSON><PERSON><PERSON> khẩu DN Ứng dụng", "Enter Bing Search V7 Endpoint": "Nhập Endpoint Bing Search V7", "Enter Bing Search V7 Subscription Key": "Nhập Khó<PERSON> đăng ký Bing Search V7", "Enter Bocha Search API Key": "Nhập Khóa API Bocha Search", "Enter Brave Search API Key": "Nhập API key cho Brave Search", "Enter certificate path": "<PERSON><PERSON><PERSON><PERSON> đường dẫn chứng chỉ", "Enter CFG Scale (e.g. 7.0)": "<PERSON><PERSON><PERSON><PERSON> (vd: 7.0)", "Enter Chunk Overlap": "Nhập <PERSON> chồng lấn (overlap)", "Enter Chunk Size": "<PERSON><PERSON><PERSON><PERSON>", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "<PERSON><PERSON><PERSON><PERSON> các cặp \"token:giá_trị_bias\" đư<PERSON><PERSON> phân tách bằng dấu phẩy (ví dụ: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter coordinates (e.g. 51.505, -0.09)": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "<PERSON><PERSON><PERSON><PERSON> mô tả", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "Nhập URL Máy chủ <PERSON>ling", "Enter Document Intelligence Endpoint": "Nhập Endpoint Tr<PERSON> tuệ <PERSON>ài li<PERSON>u", "Enter Document Intelligence Key": "<PERSON><PERSON><PERSON><PERSON> Trí tuệ Tài liệu", "Enter domains separated by commas (e.g., example.com,site.org)": "<PERSON><PERSON><PERSON><PERSON> các tên miền được phân tách bằng dấu phẩy (ví dụ: example.com,site.org)", "Enter Exa API Key": "Nhập Khóa API Exa", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Nhập URL cho Github Raw", "Enter Google PSE API Key": "Nhập Google PSE API Key", "Enter Google PSE Engine Id": "Nhập Google PSE Engine Id", "Enter hex color (e.g. #FF0000)": "", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON> (vd: 512x512)", "Enter Jina API Key": "Nhập Khóa API Jina", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Jupyter Token": "<PERSON><PERSON><PERSON><PERSON>", "Enter Jupyter URL": "Nhập URL Jupyter", "Enter Kagi Search API Key": "Nhập Khóa API Kagi Search", "Enter Key Behavior": "Nhậ<PERSON> vi phím", "Enter language codes": "<PERSON><PERSON><PERSON><PERSON> mã ngôn ngữ", "Enter Mistral API Key": "Nhập Khóa API Mistral", "Enter Model ID": "Nhập <PERSON>nh", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON><PERSON><PERSON> thẻ mô hình (vd: {{modelTag}})", "Enter Mojeek Search API Key": "Nhập Khóa API Mojeek Search", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON> (vd: 50)", "Enter Ollama Cloud API Key": "", "Enter Perplexity API Key": "Nhập Khóa API Perplexity", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Nhập URL proxy (vd: **************************:port)", "Enter reasoning effort": "<PERSON><PERSON><PERSON><PERSON> nỗ lực suy luận", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON><PERSON> (vd: <PERSON><PERSON><PERSON> a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON><PERSON>r (vd: Ka<PERSON><PERSON>)", "Enter Score": "Nhập Score", "Enter SearchApi API Key": "Nhập Khóa API SearchApi", "Enter SearchApi Engine": "Nhập Engine SearchApi", "Enter Searxng Query URL": "Nhập Query URL cho Searxng", "Enter Seed": "<PERSON><PERSON><PERSON><PERSON>", "Enter SerpApi API Key": "Nhập Khóa API SerpApi", "Enter SerpApi Engine": "Nhập Engine SerpApi", "Enter Serper API Key": "Nhập Serper API Key", "Enter Serply API Key": "Nhập Serply API Key", "Enter Serpstack API Key": "Nhập Serpstack API Key", "Enter server host": "<PERSON>hập host m<PERSON><PERSON> chủ", "Enter server label": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n m<PERSON> chủ", "Enter server port": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng m<PERSON> chủ", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "<PERSON><PERSON>ập stop sequence", "Enter system prompt": "Nhập system prompt", "Enter system prompt here": "Nhập system prompt tại đây", "Enter Tavily API Key": "Nhập Tavily API Key", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Nhập URL công khai của WebUI của bạn. URL này sẽ được sử dụng để tạo liên kết trong các thông báo.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Nhập URL cho  Tika Server", "Enter timeout in seconds": "<PERSON><PERSON><PERSON><PERSON> thời gian chờ tính bằng giây", "Enter to Send": "Enter <PERSON><PERSON>", "Enter Top K": "Nhập Top K", "Enter Top K Reranker": "<PERSON><PERSON><PERSON><PERSON> <PERSON>rank<PERSON>", "Enter URL (e.g. http://127.0.0.1:7860/)": "Nhập URL (vd: http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Nhập URL (vd: http://localhost:11434)", "Enter value": "", "Enter value (true/false)": "", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your code here...": "<PERSON><PERSON><PERSON><PERSON> mã của bạn tại đây...", "Enter your current password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u hiện tại của bạn", "Enter Your Email": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> b<PERSON>n", "Enter Your Full Name": "<PERSON><PERSON><PERSON><PERSON> và Tên của bạn", "Enter your gender": "", "Enter your message": "<PERSON><PERSON><PERSON><PERSON> tin nhắn của bạn", "Enter your name": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "Enter Your Name": "", "Enter your new password": "<PERSON><PERSON><PERSON><PERSON> mật khẩu mới của bạn", "Enter Your Password": "<PERSON><PERSON><PERSON><PERSON> của bạn", "Enter Your Role": "<PERSON><PERSON><PERSON><PERSON> vai trò của bạn", "Enter Your Username": "<PERSON><PERSON><PERSON><PERSON>ê<PERSON> đăng nhập của bạn", "Enter your webhook URL": "Nhập URL webhook của bạn", "Entra ID": "", "Error": "Lỗi", "ERROR": "LỖI", "Error accessing directory": "", "Error accessing Google Drive: {{error}}": "Lỗi truy cập <PERSON> Drive: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Lỗi tải lên tệp: {{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "", "Evaluations": "Đánh giá", "Everyone": "", "Exa API Key": "Khóa API Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Ví dụ: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Ví dụ: TẤT CẢ", "Example: mail": "<PERSON><PERSON> dụ: mail", "Example: ou=users,dc=foo,dc=example": "Ví dụ: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Ví dụ: sAMAccountName hoặc uid hoặc userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "<PERSON><PERSON> vượt quá số lượng chỗ ngồi trong giấy phép của bạn. Vui lòng liên hệ bộ phận hỗ trợ để tăng số lượng chỗ ngồi.", "Exclude": "Lo<PERSON><PERSON> trừ", "Execute code for analysis": "<PERSON>h<PERSON><PERSON> thi mã để phân tích", "Executing **{{NAME}}**...": "<PERSON><PERSON> thực thi **{{NAME}}**...", "Expand": "Mở rộng", "Experimental": "<PERSON><PERSON><PERSON>", "Explain": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>ch", "Explore the cosmos": "Khám phá vũ trụ", "Export": "<PERSON><PERSON><PERSON>", "Export All Archived Chats": "<PERSON><PERSON><PERSON> cả Chat <PERSON>ã <PERSON> trữ", "Export All Chats (All Users)": "<PERSON><PERSON><PERSON> về tất cả nội dung chat (tất cả mọi người)", "Export chat (.json)": "<PERSON><PERSON><PERSON> chat (.json)", "Export Chats": "<PERSON><PERSON><PERSON> nội dung chat về máy", "Export Config to JSON File": "<PERSON><PERSON><PERSON> h<PERSON>nh ra Tệp JSON", "Export Presets": "<PERSON><PERSON><PERSON>", "Export Prompt Suggestions": "", "Export to CSV": "Xuất ra CSV", "Export Users": "", "External": "<PERSON><PERSON><PERSON>", "External Document Loader URL required.": "", "External Task Model": "", "External Tools": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "<PERSON><PERSON><PERSON><PERSON> thể thêm tệp.", "Failed to connect to {{URL}} OpenAPI tool server": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến máy chủ công cụ OpenAPI {{URL}}", "Failed to copy link": "", "Failed to create API Key.": "Lỗi khởi tạo API Key", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "<PERSON><PERSON><PERSON><PERSON> thể lấy danh sách mô hình", "Failed to generate title": "", "Failed to import models": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to move chat": "", "Failed to read clipboard contents": "<PERSON><PERSON>ông thể đọc nội dung clipboard", "Failed to render diagram": "", "Failed to save connections": "<PERSON><PERSON><PERSON><PERSON> thể lưu các kết n<PERSON>i", "Failed to save conversation": "<PERSON><PERSON><PERSON><PERSON> thể lưu cuộc trò chuy<PERSON>n", "Failed to save models configuration": "<PERSON><PERSON><PERSON><PERSON> thể lưu cấu hình mô hình", "Failed to update settings": "Lỗi khi cập nhật các cài đặt", "Failed to upload file.": "<PERSON><PERSON><PERSON><PERSON> thể tải lên tệp.", "fast": "", "Features": "<PERSON><PERSON><PERSON>", "Features Permissions": "<PERSON><PERSON><PERSON><PERSON>", "February": "Tháng 2", "Feedback Details": "", "Feedback History": "<PERSON><PERSON><PERSON> s<PERSON> hồ<PERSON>", "Feedbacks": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "Feel free to add specific details": "<PERSON><PERSON> tả chi tiết về chất lượng của câu hỏi và phương án trả lời", "Female": "", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON><PERSON> tệp thành công.", "File content updated successfully.": "<PERSON><PERSON><PERSON> dung tệ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công.", "File Mode": "<PERSON><PERSON> độ T<PERSON><PERSON> vă<PERSON> bản", "File not found.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp.", "File removed successfully.": "<PERSON><PERSON><PERSON> t<PERSON>p thành công.", "File size should not exceed {{maxSize}} MB.": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tệp không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "<PERSON><PERSON><PERSON> t<PERSON>p lên thành công", "Files": "<PERSON><PERSON><PERSON>", "Filter": "", "Filter is now globally disabled": "<PERSON><PERSON> lọc hiện đã bị vô hiệu hóa trên toàn hệ thống", "Filter is now globally enabled": "<PERSON><PERSON> lọc hiện đ<PERSON><PERSON><PERSON> kích hoạt trên toàn hệ thống", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "<PERSON><PERSON><PERSON> hiện giả mạo vân tay: <PERSON><PERSON>ông thể sử dụng tên viết tắt làm hình đại diện. Mặc định là hình ảnh hồ sơ mặc định.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "Tập trung vào nội dung chat", "Folder": "", "Folder Background Image": "", "Folder deleted successfully": "<PERSON><PERSON><PERSON> thư mục thành công", "Folder Name": "", "Folder name cannot be empty.": "<PERSON><PERSON><PERSON> thư mục không đư<PERSON>c để trống.", "Folder name updated successfully": "<PERSON><PERSON><PERSON> nhật tên thư mục thành công", "Folder updated successfully": "", "Folders": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "<PERSON><PERSON> theo chỉ dẫn một cách hoàn hảo", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Mở ra những con đường mới", "Form": "Bi<PERSON>u mẫu", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "<PERSON><PERSON>nh dạng các biến của bạn bằng dấu ngoặc như thế này:", "Formatting may be inconsistent from source.": "", "Forwards system user OAuth access token to authenticate": "", "Forwards system user session credentials to authenticate": "<PERSON><PERSON><PERSON><PERSON> tiếp thông tin xác thực phiên người dùng hệ thống để xác thực", "Full Context Mode": "<PERSON><PERSON> độ <PERSON><PERSON> cảnh Đ<PERSON> đủ", "Function": "Function", "Function Calling": "Gọi Function", "Function created successfully": "Function đư<PERSON><PERSON> tạo thành công", "Function deleted successfully": "Function đã bị xóa", "Function Description": "Mô tả Function", "Function ID": "ID Function", "Function imported successfully": "", "Function is now globally disabled": "Function hiện đã bị vô hiệu hóa trên toàn hệ thống", "Function is now globally enabled": "Function đã đ<PERSON><PERSON><PERSON> kích hoạt trên toàn hệ thống", "Function Name": "Tên Function", "Function updated successfully": "Function đư<PERSON><PERSON> cập nhật thành công", "Functions": "Functions", "Functions allow arbitrary code execution.": "Các Function cho phép thực thi mã tùy ý.", "Functions imported successfully": "Các function đã được nạp thành công", "Gemini": "Gemini", "Gemini API Config": "<PERSON>ấu hình API Gemini", "Gemini API Key is required.": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> Khóa API Gemini.", "Gender": "", "General": "<PERSON>ài đặt chung", "Generate": "", "Generate an image": "<PERSON><PERSON><PERSON> một hình <PERSON>nh", "Generate Image": "<PERSON><PERSON>", "Generate prompt pair": "Tạo cặp prompt", "Generated Image": "", "Generating search query": "<PERSON><PERSON><PERSON> truy vấn tìm kiếm", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "<PERSON><PERSON><PERSON> đ<PERSON>u", "Get started with {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON> đầu với {{WEBUI_NAME}}", "Global": "<PERSON><PERSON><PERSON> h<PERSON> thống", "Good Response": "<PERSON><PERSON><PERSON> lời tốt", "Google Drive": "Google Drive", "Google PSE API Key": "Khóa API Google PSE", "Google PSE Engine Id": "ID công cụ Google PSE", "Gravatar": "", "Group": "Nhóm", "Group created successfully": "<PERSON><PERSON> tạo nhóm thành công", "Group deleted successfully": "Đ<PERSON> xóa nhóm thành công", "Group Description": "<PERSON><PERSON>", "Group Name": "<PERSON><PERSON><PERSON>", "Group updated successfully": "<PERSON><PERSON> cập nhật nhóm thành công", "Groups": "Nhóm", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "<PERSON><PERSON><PERSON> h<PERSON> x<PERSON> g<PERSON>c", "Headers": "", "Headers must be a valid JSON object": "", "Height": "", "Hello, {{name}}": "<PERSON>n chào {{name}}", "Help": "<PERSON><PERSON><PERSON> g<PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "<PERSON><PERSON><PERSON><PERSON> chúng tôi tạo bảng xếp hạng cộng đồng tốt nhất bằng cách chia sẻ lịch sử phản hồi của bạn!", "Hex Color": "<PERSON><PERSON><PERSON>", "Hex Color - Leave empty for default color": "<PERSON><PERSON><PERSON> - <PERSON><PERSON> trống để dùng màu mặc định", "Hide": "Ẩn", "Hide from Sidebar": "", "Hide Model": "Ẩn <PERSON><PERSON> hình", "High": "", "High Contrast Mode": "", "Home": "Trang chủ", "Host": "Host", "How can I help you today?": "Tôi có thể giúp gì cho bạn hôm nay?", "How would you rate this response?": "Bạn đánh giá phản hồi này thế nào?", "HTML": "", "Hybrid Search": "<PERSON><PERSON><PERSON> k<PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Tôi thừa nhận rằng tôi đã đọc và tôi hiểu ý nghĩa của hành động của mình. Tôi nhận thức được những rủi ro liên quan đến việc thực thi mã tùy ý và tôi đã xác minh độ tin cậy của nguồn.", "ID": "ID", "ID cannot contain \":\" or \"|\" characters": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "<PERSON><PERSON><PERSON><PERSON> dậy sự tò mò", "Image": "Ảnh", "Image Compression": "<PERSON><PERSON> Ảnh", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Tạo Ảnh", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON> (thử nghiệm)", "Image Generation Engine": "<PERSON><PERSON>ng cụ tạo <PERSON>", "Image Max Compression Size": "<PERSON><PERSON><PERSON> Ảnh Tối đa", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Tạo Prompt Ảnh", "Image Prompt Generation Prompt": "Prompt Tạo Prompt Ảnh", "Image Settings": "Cài đặt ảnh", "Images": "<PERSON><PERSON><PERSON>", "Import": "", "Import Chats": "<PERSON><PERSON><PERSON> lại nội dung chat", "Import Config from JSON File": "<PERSON><PERSON><PERSON><PERSON><PERSON> hình từ Tệp JSON", "Import From Link": "", "Import Notes": "", "Import Presets": "<PERSON><PERSON><PERSON><PERSON>", "Import Prompt Suggestions": "", "Import successful": "", "Important Update": "<PERSON><PERSON><PERSON> cập nhật quan trọng", "In order to force OCR, performing OCR must be enabled.": "", "Include": "<PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON> gồm cờ `--api-auth` khi chạy stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON> gồm flag `--api` khi chạy stable-diffusion-webui", "Includes SharePoint": "Bao gồm SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Ảnh hưởng đến tốc độ thuật toán phản hồi lại phản hồi từ văn bản được tạo. Tốc độ học thấp hơn sẽ dẫn đến các điều chỉnh chậm hơn, trong khi tốc độ học cao hơn sẽ làm cho thuật toán phản ứng nhanh hơn.", "Info": "Thông tin", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "<PERSON><PERSON><PERSON> toàn bộ nội dung vào làm ngữ cảnh để xử lý toàn diện, đi<PERSON><PERSON> nà<PERSON> đ<PERSON><PERSON><PERSON> khu<PERSON>ến nghị cho các truy vấn phức tạp.", "Input": "", "Input commands": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> c<PERSON> l<PERSON>", "Input Key (e.g. text, unet_name, steps)": "", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Insert Suggestion Prompt to Input": "", "Install from Github URL": "<PERSON>ài đặt từ URL Github", "Instant Auto-Send After Voice Transcription": "Tự động gửi ngay lập tức sau khi phiên dịch giọng nói", "Integration": "<PERSON><PERSON><PERSON>", "Integrations": "", "Interface": "<PERSON><PERSON><PERSON>", "Invalid file content": "", "Invalid file format.": "<PERSON><PERSON><PERSON> dạng tệp không hợp lệ.", "Invalid JSON file": "", "Invalid JSON format for ComfyUI Workflow.": "", "Invalid JSON format for Parameters": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "<PERSON> không hợp lệ", "is typing...": "đang gõ...", "Italic": "", "January": "Tháng 1", "Jina API Key": "Khóa API Jina", "join our Discord for help.": "tham gia Discord của chúng tôi để được trợ giúp.", "JSON": "JSON", "JSON Preview": "<PERSON><PERSON> JSON", "JSON Spec": "", "July": "Tháng 7", "June": "Tháng 6", "Jupyter Auth": "<PERSON><PERSON><PERSON>", "Jupyter URL": "URL Jupyter", "JWT Expiration": "JWT <PERSON> h<PERSON>n", "JWT Token": "Token JWT", "Kagi Search API Key": "Khóa API Kagi Search", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "Khóa", "Key is required": "", "Keyboard shortcuts": "<PERSON><PERSON><PERSON>", "Knowledge": "<PERSON><PERSON><PERSON> th<PERSON>", "Knowledge Access": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>ức", "Knowledge Base": "", "Knowledge created successfully.": "<PERSON><PERSON> tạo kiến thức thành công.", "Knowledge deleted successfully.": "<PERSON><PERSON> xóa kiến thức thành công.", "Knowledge Description": "", "Knowledge Name": "", "Knowledge Public Sharing": "<PERSON><PERSON> sẻ <PERSON><PERSON>ng khai <PERSON> thức", "Knowledge reset successfully.": "<PERSON><PERSON> đặt lại kiến thức thành công.", "Knowledge updated successfully": "<PERSON><PERSON> cập nhật kiến thức thành công", "Kokoro.js (Browser)": "Kokoro.js (<PERSON><PERSON><PERSON><PERSON>)", "Kokoro.js Dtype": "Ki<PERSON>u dữ liệu <PERSON>.js", "Label": "<PERSON><PERSON>ã<PERSON>", "Landing Page Mode": "<PERSON>ế độ <PERSON>", "Language": "<PERSON><PERSON><PERSON>", "Language Locales": "", "Last Active": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> g<PERSON><PERSON> n<PERSON>t", "Last Modified": "<PERSON><PERSON><PERSON> s<PERSON>a gần nh<PERSON>t", "Last reply": "<PERSON><PERSON><PERSON> lời cu<PERSON>i", "LDAP": "LDAP", "LDAP server updated": "<PERSON><PERSON> cập nhật máy chủ LDAP", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON> hạng", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "<PERSON><PERSON> trống nếu không giới hạn", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "<PERSON><PERSON> trống để bao gồm tất cả các mô hình từ điểm cuối \"{{url}}/api/tags\"", "Leave empty to include all models from \"{{url}}/models\" endpoint": "<PERSON><PERSON> trống để bao gồm tất cả các mô hình từ điểm cuối \"{{url}}/models\"", "Leave empty to include all models or select specific models": "<PERSON><PERSON> trống để bao gồm tất cả các mô hình hoặc chọn các mô hình cụ thể", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON> trống để sử dụng prompt mặc định, hoặc nhập prompt tùy chỉnh", "Leave model field empty to use the default model.": "<PERSON><PERSON> trống trường mô hình để sử dụng mô hình mặc định.", "Legacy": "", "lexical": "", "License": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>p", "Lift List": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "<PERSON><PERSON> nghe...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "<PERSON><PERSON> thống có thể tạo ra nội dung không chính xác hoặc sai. H<PERSON>y kiểm chứng kỹ lưỡng thông tin trước khi tiếp nhận và sử dụng.", "Loader": "<PERSON><PERSON><PERSON><PERSON>", "Loading Kokoro.js...": "<PERSON><PERSON> t<PERSON>.js...", "Loading...": "<PERSON><PERSON> tả<PERSON>...", "Local": "<PERSON><PERSON><PERSON> bộ", "Local Task Model": "", "Location access not allowed": "<PERSON><PERSON><PERSON><PERSON> cho phép truy cập vị trí", "Lost": "Thua", "Low": "", "LTR": "LTR", "Made by Open WebUI Community": "<PERSON><PERSON><PERSON><PERSON> tạo bởi Cộng đồng OpenWebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "<PERSON><PERSON><PERSON> ch<PERSON>c chắn bao quanh chúng bằng", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON> b<PERSON>o xuất tệp Workflow.json đúng format API của ComfyUI.", "Male": "", "Manage": "<PERSON><PERSON><PERSON><PERSON> lý", "Manage Direct Connections": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> n<PERSON>i <PERSON> tiếp", "Manage Models": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> h<PERSON>nh", "Manage Ollama": "<PERSON><PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> nối API Ollama", "Manage OpenAI API Connections": "<PERSON><PERSON><PERSON>n l<PERSON> nối API OpenAI", "Manage Pipelines": "Quản lý Pipelines", "Manage Tool Servers": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> chủ <PERSON>ng cụ", "Manage your account information.": "", "March": "Tháng 3", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "<PERSON><PERSON> lượ<PERSON>ải lên Tối đa", "Max Upload Size": "<PERSON><PERSON><PERSON><PERSON><PERSON> lên <PERSON> đa", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Tối đa 3 mô hình có thể được tải xuống cùng lúc. <PERSON><PERSON> lòng thử lại sau.", "May": "Tháng 5", "MCP": "", "MCP support is experimental and its specification changes often, which can lead to incompatibilities. OpenAPI specification support is directly maintained by the Open WebUI team, making it the more reliable option for compatibility.": "", "Medium": "", "Memories accessible by LLMs will be shown here.": "Memory có thể truy cập bởi LLMs sẽ hiển thị ở đây.", "Memory": "Memory", "Memory added successfully": "<PERSON> đã đ<PERSON><PERSON><PERSON> thêm thành công", "Memory cleared successfully": "<PERSON> đã bị xóa", "Memory deleted successfully": "Memory đã bị loại bỏ", "Memory updated successfully": "<PERSON> đã cập nhật thành công", "Merge Responses": "<PERSON><PERSON><PERSON> n<PERSON> c<PERSON>c p<PERSON><PERSON>n hồi", "Merged Response": "<PERSON><PERSON><PERSON> h<PERSON>", "Message rating should be enabled to use this feature": "<PERSON><PERSON><PERSON> bật tính năng đánh giá tin nhắn để sử dụng tính năng này", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Tin nhắn bạn gửi sau khi tạo liên kết sẽ không được chia sẻ. Người dùng có URL sẽ có thể xem cuộc trò chuyện được chia sẻ.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "<PERSON><PERSON><PERSON> c<PERSON>u Khóa API Mistral OCR.", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Mô hình '{{modelName}}' đã đư<PERSON><PERSON> tải xuống thành công.", "Model '{{modelTag}}' is already in queue for downloading.": "Mô hình '{{modelTag}}' đã có trong hàng đợi để tải xuống.", "Model {{modelId}} not found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON>ô hình {{modelId}}", "Model {{modelName}} is not vision capable": "Model {{modelName}} không có khả năng nhìn", "Model {{name}} is now {{status}}": "Model {{name}} bây giờ là {{status}}", "Model {{name}} is now hidden": "<PERSON><PERSON> h<PERSON>nh {{name}} hiện đã bị ẩn", "Model {{name}} is now visible": "<PERSON><PERSON> hình {{name}} hiện có thể nhìn thấy", "Model accepts file inputs": "", "Model accepts image inputs": "<PERSON><PERSON> hình chấp nhận đầu vào hình <PERSON>nh", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "<PERSON> đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Đường dẫn hệ thống tệp mô hình đư<PERSON><PERSON> phát hiện. Tên viết tắt mô hình là bắt buộc để cập nhật, không thể tiếp tục.", "Model Filtering": "<PERSON><PERSON><PERSON>", "Model ID": "ID mẫu", "Model ID is required.": "", "Model IDs": "Các <PERSON> h<PERSON>nh", "Model Name": "<PERSON><PERSON><PERSON>", "Model name already exists, please choose a different one": "", "Model Name is required.": "", "Model not selected": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>nh", "Model Params": "<PERSON><PERSON>", "Model Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Model unloaded successfully": "", "Model updated successfully": "<PERSON> đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "Model(s) do not support file upload": "", "Modelfile Content": "<PERSON><PERSON><PERSON> dung <PERSON><PERSON><PERSON>nh", "Models": "<PERSON><PERSON>", "Models Access": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>nh", "Models configuration saved successfully": "<PERSON><PERSON> lưu cấu hình mô hình thành công", "Models imported successfully": "", "Models Public Sharing": "<PERSON><PERSON> sẻ <PERSON>ông khai <PERSON>ô hình", "Mojeek Search API Key": "Khóa API Mojeek Search", "More": "<PERSON><PERSON><PERSON><PERSON>", "More Concise": "", "More Options": "", "Move": "", "Name": "<PERSON><PERSON><PERSON>", "Name and ID are required, please fill them out": "", "Name your knowledge base": "Đặt tên cho cơ sở kiến thức của bạn", "Native": "Gố<PERSON>", "New Button": "", "New Chat": "Tạo chat mới", "New Folder": "<PERSON><PERSON><PERSON>", "New Function": "", "New Knowledge": "", "New Model": "", "New Note": "", "New Password": "<PERSON><PERSON><PERSON> mới", "New Prompt": "", "New Tool": "", "new-channel": "kênh-mới", "Next message": "", "No authentication": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nội dung", "No content found in file.": "", "No content to speak": "<PERSON><PERSON><PERSON><PERSON> có nội dung để nói", "No conversation to save": "", "No distance available": "<PERSON><PERSON><PERSON><PERSON> có khoảng cách khả dụng", "No feedbacks found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phản hồi nào", "No file selected": "<PERSON><PERSON><PERSON> có tệp nào đ<PERSON><PERSON><PERSON> ch<PERSON>n", "No functions found": "", "No groups with access, add a group to grant access": "<PERSON><PERSON><PERSON><PERSON> có nhóm nào có quyền truy cập, h<PERSON><PERSON> thêm một nhóm để cấp quyền truy cập", "No HTML, CSS, or JavaScript content found.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nội dung HTML, CSS hoặc JavaScript.", "No inference engine with management support found": "<PERSON><PERSON><PERSON>ng tìm thấy engine suy luận nào có hỗ trợ quản lý", "No knowledge found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kiến thức", "No memories to clear": "<PERSON><PERSON><PERSON><PERSON> có bộ nhớ nào để xóa", "No model IDs": "<PERSON><PERSON><PERSON><PERSON> có <PERSON> mô hình", "No models found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mô hình nào", "No models selected": "<PERSON><PERSON><PERSON> chọn mô hình nào", "No Notes": "", "No notes found": "", "No prompts found": "", "No results": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "No results found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "No search query generated": "<PERSON><PERSON><PERSON><PERSON> có truy vấn tìm kiếm nào đư<PERSON><PERSON> tạo ra", "No source available": "<PERSON><PERSON><PERSON><PERSON> có nguồn", "No sources found": "", "No suggestion prompts": "<PERSON><PERSON><PERSON><PERSON> có prompt gợi ý", "No tools found": "", "No users were found.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng nào.", "No valves": "", "No valves to update": "<PERSON><PERSON><PERSON> c<PERSON> <PERSON> nào đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "Node Ids": "", "None": "Không ai", "Not factually correct": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h xác so với thực tế", "Not helpful": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>u <PERSON>", "Not Registered": "", "Note": "", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Lưu ý: <PERSON><PERSON><PERSON> bạn đặt điểm (Score) tối thiểu thì tìm kiếm sẽ chỉ trả về những tài liệu có điểm lớn hơn hoặc bằng điểm tối thiểu.", "Notes": "<PERSON><PERSON><PERSON>", "Notes Public Sharing": "", "Notification Sound": "<PERSON><PERSON> <PERSON> báo", "Notification Webhook": "Webhook Thông báo", "Notifications": "<PERSON><PERSON><PERSON><PERSON> báo trên má<PERSON> t<PERSON> (Notification)", "November": "Tháng 11", "OAuth": "", "OAuth 2.1": "", "OAuth ID": "ID OAuth", "October": "Tháng 10", "Off": "Tắt", "Okay, Let's Go!": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> đầu thôi!", "OLED Dark": "OLED Dark", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API settings updated": "<PERSON><PERSON> cập nhật cài đặt API Ollama", "Ollama Cloud API Key": "", "Ollama Version": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "On": "<PERSON><PERSON><PERSON>", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Chỉ cho phép các ký tự chữ và số và dấu gạch nối", "Only alphanumeric characters and hyphens are allowed in the command string.": "Chỉ ký tự số và gạch nối được phép trong chuỗi lệnh.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Chỉ có thể chỉnh sửa bộ sưu tập, tạo cơ sở kiến thức mới để chỉnh sửa/thêm tài liệu.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Chỉ người dùng và nhóm được chọn có quyền mới có thể truy cập", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Rất tiếc! URL dường như không hợp lệ. <PERSON>ui lòng kiểm tra lại và thử lại.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ối! Vẫn còn tệp đang tải lên. <PERSON><PERSON> lòng đợi quá trình tải lên hoàn tất.", "Oops! There was an error in the previous response.": "Ối! Đ<PERSON> xảy ra lỗi trong phản hồi trước đó.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Rất tiếc! Bạn đang sử dụng một phương thức không được hỗ trợ (chỉ dành cho frontend). <PERSON><PERSON> lòng cung cấp phương thức cho WebUI từ phía backend.", "Open file": "Mở tệp", "Open in full screen": "Mở toàn màn hình", "Open link": "", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open Modal To Manage Image Compression": "", "Open new chat": "Mở nội dung chat mới", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI sử dụng faster-whisper bên trong.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI sử dụng SpeechT5 và các embedding giọng nói CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Phiên bản Open WebUI (v{{OPEN_WEBUI_VERSION}}) hiện thấp hơn phiên bản bắt buộc (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Cấu hình API OpenAI", "OpenAI API Key is required.": "Bắt bu<PERSON>c nhập API OpenAI Key.", "OpenAI API settings updated": "<PERSON><PERSON> cập nhật cài đặt API OpenAI", "OpenAI URL/Key required.": "<PERSON><PERSON>u cầu URL/Key API OpenAI.", "OpenAPI": "", "OpenAPI Spec": "", "openapi.json URL or Path": "", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "hoặc", "Ordered List": "", "Organize your users": "<PERSON><PERSON> chức người dùng của bạn", "Other": "K<PERSON><PERSON><PERSON>", "OUTPUT": "ĐẦU RA", "Output format": "<PERSON><PERSON><PERSON> dạng đầu ra", "Output Format": "", "Overview": "<PERSON><PERSON><PERSON> quan", "page": "trang", "Paginate": "", "Parameters": "", "Password": "<PERSON><PERSON><PERSON>", "Passwords do not match.": "", "Paste Large Text as File": "<PERSON><PERSON> bản Lớn dưới dạng T<PERSON>p", "PDF Backend": "", "PDF document (.pdf)": "Tập tin PDF (.pdf)", "PDF Extract Images (OCR)": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON> từ PDF (OCR)", "pending": "đang chờ phê du<PERSON>t", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Perform OCR": "", "Permission denied when accessing media devices": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>y cập các thiết bị đa phư<PERSON>ng tiện bị từ chối", "Permission denied when accessing microphone": "<PERSON><PERSON><PERSON><PERSON> truy cập micrô bị từ chối", "Permission denied when accessing microphone: {{error}}": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>y cập micr<PERSON> bị từ chối: {{error}}", "Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Perplexity API Key": "Khóa API Perplexity", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Cá nhân hóa", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "<PERSON><PERSON>", "Pinned": "Đã ghim", "Pioneer insights": "<PERSON><PERSON><PERSON><PERSON> phong về hiểu biết", "Pipe": "", "Pipeline": "", "Pipeline deleted successfully": "Đã xóa pipeline thành công", "Pipeline downloaded successfully": "Đã tải xuống pipeline thành công", "Pipelines": "Pipelines", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines là hệ thống plugin cho phép thực thi mã tùy ý —", "Pipelines Not Detected": "<PERSON><PERSON>a tìm thấy Pi<PERSON>ines", "Pipelines Valves": "<PERSON><PERSON><PERSON> c<PERSON>", "Plain text (.md)": "", "Plain text (.txt)": "<PERSON><PERSON><PERSON> bản thô (.txt)", "Playground": "<PERSON><PERSON><PERSON> (Playground)", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "<PERSON>ui lòng xem xét cẩn thận các cảnh báo sau:", "Please do not close the settings page while loading the model.": "<PERSON><PERSON> lòng không đóng trang cài đặt trong khi tải mô hình.", "Please enter a message or attach a file.": "", "Please enter a prompt": "<PERSON><PERSON> lòng nh<PERSON><PERSON> một prompt", "Please enter a valid ID": "", "Please enter a valid JSON spec": "", "Please enter a valid path": "<PERSON><PERSON> lòng nhập một đường dẫn hợp lệ", "Please enter a valid URL": "<PERSON><PERSON> lòng nhậ<PERSON> một URL hợp lệ", "Please enter a valid URL.": "", "Please fill in all fields.": "<PERSON><PERSON> lòng điền vào tất cả các trường.", "Please register the OAuth client": "", "Please save the connection to persist the OAuth client information and do not change the ID": "", "Please select a model first.": "<PERSON><PERSON> lòng chọn một mô hình trước.", "Please select a model.": "<PERSON><PERSON> lòng chọn một mô hình.", "Please select a reason": "<PERSON><PERSON> lòng chọn một lý do", "Please select a valid JSON file": "", "Please wait until all files are uploaded.": "", "Port": "Cổng", "Positive attitude": "<PERSON><PERSON><PERSON><PERSON> độ tích cực", "Prefer not to say": "", "Prefix ID": "<PERSON><PERSON><PERSON><PERSON> tố <PERSON>", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Tiền tố ID được sử dụng để tránh xung đột với các kết nối khác bằng cách thêm tiền tố vào ID mô hình - để trống để tắt", "Prevent file creation": "", "Preview": "", "Previous 30 days": "30 ngày trước", "Previous 7 days": "7 ng<PERSON>y trước", "Previous message": "", "Private": "<PERSON><PERSON><PERSON><PERSON> tư", "Profile": "<PERSON><PERSON> sơ", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (ví dụ: <PERSON><PERSON><PERSON> kể cho tôi một sự thật thú vị về Đế chế La Mã)", "Prompt Autocompletion": "Tự động hoàn thành Prompt", "Prompt Content": "<PERSON>ội dung prompt", "Prompt created successfully": "<PERSON><PERSON> tạo prompt thà<PERSON> công", "Prompt suggestions": "Gợi ý prompt", "Prompt updated successfully": "<PERSON><PERSON> cậ<PERSON> nh<PERSON>t prompt thà<PERSON> công", "Prompts": "Prompt", "Prompts Access": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> Prompt", "Prompts Public Sharing": "<PERSON>a sẻ Công khai Prompt", "Provider Type": "", "Public": "<PERSON><PERSON><PERSON> khai", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON><PERSON><PERSON> \"{{searchValue}}\" từ Ollama.com", "Pull a model from Ollama.com": "Tải mô hình từ Ollama.com", "Pull Model": "", "pypdfium2": "", "Query Generation Prompt": "Prompt <PERSON><PERSON><PERSON> Truy vấn", "Querying": "", "Quick Actions": "", "RAG Template": "Mẫu prompt cho RAG", "Rating": "Đánh giá", "Re-rank models by topic similarity": "<PERSON><PERSON><PERSON> hạng lại các mô hình theo độ tương đồng chủ đề", "Read": "<PERSON><PERSON><PERSON>", "Read Aloud": "Đ<PERSON><PERSON> ra loa", "Read more →": "", "Reason": "", "Reasoning Effort": "Nỗ lực <PERSON> luận", "Reasoning Tags": "", "Record": "", "Record voice": "<PERSON><PERSON> <PERSON><PERSON>", "Redirecting you to Open WebUI Community": "<PERSON><PERSON> chuyển hướng bạn đến Cộng đồng OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "<PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> suất tạo ra nội dung vô nghĩa. <PERSON><PERSON><PERSON> trị cao hơn (ví dụ: 100) sẽ cho câu trả lời đa dạng hơn, trong khi gi<PERSON> trị thấ<PERSON> h<PERSON> (ví dụ: 10) sẽ thận trọng hơn.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON><PERSON><PERSON> coi bản thân mình như \"Người dùng\" (ví dụ: \"Người dùng đang học T<PERSON>ếng Tâ<PERSON> Ban <PERSON>ha\")", "Reference Chats": "", "Refused when it shouldn't have": "Từ chối trả lời mà nhẽ không nên làm vậy", "Regenerate": "<PERSON><PERSON><PERSON> sinh lại câu trả lời", "Regenerate Menu": "", "Register Again": "", "Register Client": "", "Registered": "", "Registration failed": "", "Registration successful": "", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "<PERSON><PERSON> tả nh<PERSON>ng cập nhật mới", "Releases": "", "Relevance": "<PERSON><PERSON><PERSON> độ liên quan", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "Xóa", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "Xóa model", "Remove this tag from list": "", "Rename": "<PERSON><PERSON><PERSON> tên", "Reorder Models": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lại <PERSON>nh", "Reply": "", "Reply in Thread": "<PERSON><PERSON><PERSON> lời trong <PERSON>", "Reply to thread...": "", "Replying to {{NAME}}": "", "required": "", "Reranking Engine": "", "Reranking Model": "Reranking Model", "Reset": "<PERSON><PERSON><PERSON> bộ", "Reset All Models": "Đặt lại Tất cả Mô hình", "Reset Image": "Đặt lại hình <PERSON>nh", "Reset Upload Directory": "<PERSON><PERSON><PERSON> to<PERSON>n bộ thư mục Upload", "Reset Vector Storage/Knowledge": "Đặt lại <PERSON> tr<PERSON>/<PERSON><PERSON><PERSON> thức", "Reset view": "Đặt lại chế độ xem", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "<PERSON>hông thể kích hoạt thông báo vì trang web không cấp quyền. <PERSON>ui lòng truy cập cài đặt trình duyệt của bạn để cấp quyền cần thiết.", "Response splitting": "<PERSON><PERSON> tách ph<PERSON>n hồi", "Response Watermark": "", "Result": "<PERSON><PERSON><PERSON> qu<PERSON>", "RESULT": "<PERSON><PERSON><PERSON> qu<PERSON>", "Retrieval": "<PERSON><PERSON><PERSON>", "Retrieval Query Generation": "<PERSON>ạo Truy vấn Truy xu<PERSON>t", "Retrieved {{count}} sources": "", "Retrieved {{count}} sources_other": "", "Retrieved 1 source": "", "Rich Text Input for Chat": "<PERSON><PERSON><PERSON><PERSON> bản <PERSON> dạng cho <PERSON>", "RK": "RK", "Role": "<PERSON>ai trò", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON>", "Running...": "<PERSON><PERSON> ch<PERSON>...", "Save": "<PERSON><PERSON><PERSON>", "Save & Create": "Lưu & Tạo", "Save & Update": "Lưu & Cập nhật", "Save As Copy": "<PERSON><PERSON><PERSON> dạng <PERSON> sao", "Save Chat": "", "Save Tag": "<PERSON><PERSON><PERSON> Thẻ", "Saved": "<PERSON><PERSON> l<PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Không còn hỗ trợ lưu trữ lịch sử chat trực tiếp vào bộ nhớ trình duyệt của bạn. Vui lòng dành thời gian để tải xuống và xóa lịch sử chat của bạn bằng cách nhấp vào nút bên dưới. <PERSON><PERSON><PERSON> lo lắng, bạn có thể dễ dàng nhập lại lịch sử chat của mình vào backend thông qua", "Scroll On Branch Change": "", "Search": "<PERSON><PERSON><PERSON>", "Search a model": "Tìm model", "Search all emojis": "", "Search Base": "C<PERSON> sở Tìm kiếm", "Search Chats": "<PERSON><PERSON><PERSON> k<PERSON> c<PERSON>", "Search Collection": "<PERSON><PERSON><PERSON> k<PERSON> sưu tập", "Search Filters": "<PERSON><PERSON> l<PERSON> k<PERSON>", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "tìm kiếm thẻ", "Search Functions": "<PERSON><PERSON><PERSON> k<PERSON>m Functions", "Search In Models": "", "Search Knowledge": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>c", "Search Models": "Tìm model", "Search Notes": "", "Search options": "<PERSON><PERSON><PERSON> chọn tìm kiếm", "Search Prompts": "Tìm prompt", "Search Result Count": "Số kết quả tìm kiếm", "Search the internet": "T<PERSON><PERSON> kiếm trên internet", "Search Tools": "<PERSON><PERSON><PERSON>", "SearchApi API Key": "Khóa API SearchApi", "SearchApi Engine": "Engine SearchApi", "Searched {{count}} sites": "<PERSON><PERSON> tìm kiếm {{count}} trang web", "Searching": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON> tìm \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON> tìm kiếm <PERSON> thức cho \"{{searchQuery}}\"", "Searching the web": "", "Searxng Query URL": "URL truy vấn Searxng", "See readme.md for instructions": "Xem readme.md để biết hướng dẫn", "See what's new": "<PERSON><PERSON> nh<PERSON> cập nhật mới", "Seed": "Seed", "Select": "", "Select a base model": "<PERSON><PERSON><PERSON> một base model", "Select a base model (e.g. llama3, gpt-4o)": "", "Select a conversation to preview": "", "Select a engine": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "Select a function": "Chọn function", "Select a group": "<PERSON><PERSON><PERSON> m<PERSON> n<PERSON>", "Select a language": "", "Select a mode": "", "Select a model": "<PERSON><PERSON><PERSON> mô hình", "Select a model (optional)": "", "Select a pipeline": "<PERSON><PERSON><PERSON> một quy trình", "Select a pipeline url": "<PERSON><PERSON>n url quy trình", "Select a reranking model engine": "", "Select a role": "", "Select a theme": "", "Select a tool": "Chọn tool", "Select a voice": "", "Select an auth method": "<PERSON><PERSON><PERSON> một ph<PERSON><PERSON><PERSON> thức xác thực", "Select an embedding model engine": "", "Select an engine": "", "Select an Ollama instance": "<PERSON><PERSON><PERSON> một phiên bả<PERSON>", "Select an output format": "", "Select dtype": "", "Select Engine": "Chọn Engine", "Select how to split message text for TTS requests": "", "Select Knowledge": "<PERSON><PERSON><PERSON> thức", "Select only one model to call": "Chọn model <PERSON><PERSON>i", "Select view": "", "Selected model(s) do not support image inputs": "Model đư<PERSON><PERSON> lựa chọn không hỗ trợ đầu vào là hình ảnh", "semantic": "", "Send": "<PERSON><PERSON><PERSON>", "Send a Message": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "Send message": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Gửi `stream_options: { include_usage: true }` trong yêu cầu.\n<PERSON><PERSON>c nhà cung cấp được hỗ trợ sẽ trả về thông tin sử dụng token trong phản hồi khi được đặt.", "September": "Tháng 9", "SerpApi API Key": "Khóa API SerpApi", "SerpApi Engine": "Engine SerpApi", "Serper API Key": "Khóa API Serper", "Serply API Key": "Khóa API Serply", "Serpstack API Key": "Khóa API Serpstack", "Server connection verified": "<PERSON><PERSON><PERSON> n<PERSON>i máy chủ đã đ<PERSON><PERSON><PERSON> xác <PERSON>h", "Session": "", "Set as default": "Đặt làm mặc định", "Set CFG Scale": "Đặt Thang CFG", "Set Default Model": "Đặt <PERSON>ô hình Mặc định", "Set embedding model": "Đặt mô hình embedding", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON> lập mô hình embedding (ví dụ: {{model}})", "Set Image Size": "Đặt <PERSON><PERSON><PERSON> thước Ảnh", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON> lập mô hình reranking (ví dụ: {{model}})", "Set Sampler": "Đặt Sampler", "Set Scheduler": "Đặt Scheduler", "Set Steps": "Đặt Số Bước", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Đặt số lượng lớp sẽ được chuyển tải sang GPU. Tăng giá trị này có thể cải thiện đáng kể hiệu suất cho các mô hình được tối ưu hóa cho tăng tốc GPU nhưng cũng có thể tiêu thụ nhiều năng lượng và tài nguyên GPU hơn.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Đặt số lượng luồng công nhân được sử dụng để tính toán. T<PERSON><PERSON> chọn này kiểm soát số lượng luồng được sử dụng để xử lý đồng thời các yêu cầu đến. Tăng giá trị này có thể cải thiện hiệu suất dưới tải công việc đồng thời cao nhưng cũng có thể tiêu thụ nhiều tài nguyên CPU hơn.", "Set Voice": "Đặt G<PERSON><PERSON>ng nói", "Set whisper model": "Đặt mô hình whisper", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Đặt một thiên vị phẳng chống lại các token đã xuất hiện ít nhất một lần. <PERSON><PERSON><PERSON> trị cao hơn (ví dụ: 1.5) sẽ phạt sự lặp lại mạnh hơn, trong khi giá trị thấp hơn (ví dụ: 0.9) sẽ khoan dung hơn. Tại 0, nó bị vô hiệu hóa.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Đặt một thiên vị tỷ lệ chống lại các token để phạt sự lặp lại, dựa trên số lần chúng đã xuất hiện. <PERSON><PERSON><PERSON> trị cao hơn (ví dụ: 1.5) sẽ phạt sự lặp lại mạnh hơn, trong khi giá trị thấp hơn (ví dụ: 0.9) sẽ khoan dung hơn. Tại 0, nó bị vô hiệu hóa.", "Sets how far back for the model to look back to prevent repetition.": "Đặt khoảng cách mà mô hình nhìn lại để ngăn chặn sự lặp lại.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Đặt hạt giống số ngẫu nhiên để sử dụng cho việc tạo. Đặt giá trị này thành một số cụ thể sẽ làm cho mô hình tạo ra cùng một văn bản cho cùng một prompt.", "Sets the size of the context window used to generate the next token.": "Đặt kích thước của cửa sổ ngữ cảnh được sử dụng để tạo token tiếp theo.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Đặt các chuỗi dừng để sử dụng. <PERSON><PERSON> gặp mẫu này, LLM sẽ ngừng tạo văn bản và trả về. <PERSON><PERSON> thể đặt nhiều mẫu dừng bằng cách chỉ định nhiều tham số stop riêng biệt trong modelfile.", "Settings": "Cài đặt", "Settings saved successfully!": "Cài đặt đã được lưu thành công!", "Share": "<PERSON><PERSON> sẻ", "Share Chat": "<PERSON><PERSON> sẻ Chat", "Share to Open WebUI Community": "<PERSON>a sẻ đến Cộng đồng OpenWebUI", "Share your background and interests": "", "Shared with you": "", "Sharing Permissions": "<PERSON><PERSON><PERSON><PERSON>a sẻ", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "<PERSON><PERSON><PERSON> thị", "Show \"What's New\" modal on login": "Hi<PERSON>n thị cửa sổ \"<PERSON>ó gì mới\" khi đăng nhập", "Show Admin Details in Account Pending Overlay": "Hiển thị thông tin của Quản trị viên trên màn hình hiển thị Tài khoản đang chờ xử lý", "Show Formatting Toolbar": "", "Show image preview": "", "Show Model": "<PERSON><PERSON><PERSON> thị <PERSON> h<PERSON>nh", "Show shortcuts": "<PERSON><PERSON><PERSON> thị phím tắt", "Show your support!": "Thể hiện sự ủng hộ của bạn!", "Showcased creativity": "<PERSON><PERSON><PERSON> hiện sự sáng tạo", "Sign in": "<PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON> nhập vào {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "<PERSON><PERSON><PERSON> nhập vào {{WEBUI_NAME}} bằng LDAP", "Sign Out": "<PERSON><PERSON><PERSON> xu<PERSON>", "Sign up": "<PERSON><PERSON><PERSON> ký", "Sign up to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON> k<PERSON> {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "<PERSON><PERSON> đ<PERSON>ng nhập vào {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Something went wrong :/": "", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON> <PERSON><PERSON> lại <PERSON> nói", "Speech recognition error: {{error}}": "Lỗi nhận dạng giọng nói: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "<PERSON><PERSON>ng cụ <PERSON>hận dạng <PERSON> nói", "standard": "", "Start of the channel": "<PERSON><PERSON><PERSON> k<PERSON>", "Start Tag": "", "Status Updates": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "Dừng", "Stop Generating": "", "Stop Sequence": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Stream Chat Response": "<PERSON><PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp <PERSON> h<PERSON>", "Stream Delta Chunk Size": "", "Streamable HTTP": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "Mô hình STT", "STT Settings": "Cài đặt Nhận dạng G<PERSON>ng nói", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "<PERSON><PERSON> đề (ví dụ: về Đế chế La Mã)", "Success": "<PERSON><PERSON><PERSON><PERSON> công", "Successfully imported {{userCount}} users.": "", "Successfully updated.": "<PERSON><PERSON> cập nhật thành công.", "Suggest a change": "", "Suggested": "<PERSON><PERSON><PERSON> ý một số mẫu prompt", "Support": "Hỗ trợ", "Support this plugin:": "Hỗ trợ plugin này:", "Supported MIME Types": "", "Sync directory": "<PERSON><PERSON><PERSON> bộ thư mục", "System": "<PERSON><PERSON> th<PERSON>", "System Instructions": "Hướng dẫn Hệ thống", "System Prompt": "Prompt <PERSON><PERSON> thống (System Prompt)", "Table Mode": "", "Tag": "", "Tags": "Thẻ", "Tags Generation": "Tạo Thẻ", "Tags Generation Prompt": "Prompt Tạo Thẻ", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Lấy mẫu không đuôi (tail free sampling) được sử dụng để giảm tác động của các token ít có khả năng xuất hiện trong đầu ra. <PERSON><PERSON><PERSON> trị cao h<PERSON> (ví dụ: 2.0) sẽ giảm tác động nhiều hơn, trong khi giá trị 1.0 sẽ tắt cài đặt này.", "Talk to model": "<PERSON><PERSON><PERSON> chuy<PERSON>n với mô hình", "Tap to interrupt": "<PERSON><PERSON><PERSON> đ<PERSON> ng<PERSON>", "Task List": "", "Task Model": "", "Tasks": "Tác vụ", "Tavily API Key": "Khóa API Tavily", "Tavily Extract Depth": "", "Tell us more:": "<PERSON><PERSON><PERSON> cho chúng tôi hiểu thêm về chất lư<PERSON><PERSON> của câu trả lời:", "Temperature": "<PERSON><PERSON><PERSON> sáng tạo", "Temporary Chat": "<PERSON><PERSON>", "Temporary Chat by Default": "", "Text Splitter": "<PERSON><PERSON> chia <PERSON> bản", "Text-to-Speech": "", "Text-to-Speech Engine": "<PERSON><PERSON>ng cụ <PERSON>n Văn bản thành <PERSON> nói", "Thanks for your feedback!": "<PERSON><PERSON>m ơn bạn đã gửi phản hồi!", "The Application Account DN you bind with for search": "DN Tài khoản Ứng dụng bạn liên kết để tìm kiếm", "The base to search for users": "<PERSON><PERSON> sở để tìm kiếm người dùng", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "<PERSON><PERSON><PERSON> thước lô xác định có bao nhiêu yêu cầu văn bản được xử lý cùng một lúc. <PERSON><PERSON><PERSON> thước lô cao hơn có thể tăng hiệu suất và tốc độ của mô hình, nhưng nó cũng đòi hỏi nhiều bộ nhớ hơn.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "<PERSON><PERSON><PERSON> nhà phát triển đằng sau plugin này là những tình nguyện viên nhiệt huyết của cộng đồng. Nếu bạn thấy plugin này hữu <PERSON>, vui lòng cân nhắc đóng góp cho sự phát triển của nó.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "<PERSON>ảng xếp hạng đánh giá dựa trên hệ thống xếp hạng <PERSON>o và đư<PERSON> cập nhật theo thời gian thực.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "<PERSON><PERSON><PERSON><PERSON> tính LDAP ánh xạ tới mail mà người dùng sử dụng để đăng nhập.", "The LDAP attribute that maps to the username that users use to sign in.": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h LDAP ánh xạ tới tên người dùng mà người dùng sử dụng để đăng nhập.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Bảng xếp hạng hiện đang trong giai đoạn beta và chúng tôi có thể điều chỉnh các tính toán xếp hạng khi chúng tôi tinh chỉnh thuật toán.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "<PERSON><PERSON><PERSON> thước tệp tối đa tính bằng MB. <PERSON><PERSON><PERSON> kích thước tệp vượt quá giới hạn này, tệp sẽ không được tải lên.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Số lượng tệp tối đa có thể được sử dụng cùng một lúc trong cuộc trò chuyện. Nếu số lượng tệp vượt quá giới hạn này, các tệp sẽ không được tải lên.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "<PERSON><PERSON><PERSON><PERSON> (score) ph<PERSON>i có giá trị từ 0,0 (0%) đến 1,0 (100%).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "Nhiệt độ của mô hình. Tăng nhiệt độ sẽ làm cho mô hình trả lời sáng tạo hơn.", "The Weight of BM25 Hybrid Search. 0 more semantic, 1 more lexical. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Chủ đề", "Thinking...": "<PERSON><PERSON> suy luận...", "This action cannot be undone. Do you wish to continue?": "Hành động này không thể được hoàn tác. Bạn có muốn tiếp tục không?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "<PERSON><PERSON><PERSON> này được tạo vào {{createdAt}}. <PERSON><PERSON><PERSON> là điểm khởi đầu của kênh {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "<PERSON><PERSON><PERSON><PERSON> trò chuyện này sẽ không xuất hiện trong lịch sử và tin nhắn của bạn sẽ không đượ<PERSON> lưu.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON>i<PERSON>u này đảm bảo rằng các nội dung chat có giá trị của bạn được lưu an toàn vào cơ sở dữ liệu backend của bạn. Cảm ơn bạn!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is a default user permission and will remain enabled.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "<PERSON><PERSON><PERSON> là tính năng thử nghiệm, có thể không hoạt động như mong đợi và có thể thay đổi bất kỳ lúc nào.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "T<PERSON>y chọn này kiểm soát số lượng token được bảo tồn khi làm mới ngữ cảnh. Ví dụ: nếu đặt thành 2, 2 token cuối cùng của ngữ cảnh hội thoại sẽ được giữ lại. Bảo tồn ngữ cảnh có thể giúp duy trì tính liên tục của cuộc trò chuy<PERSON>n, nhưng nó có thể làm giảm khả năng phản hồi các chủ đề mới.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "T<PERSON><PERSON> chọn này đặt số lượng token tối đa mà mô hình có thể tạo ra trong phản hồi của nó. Tăng giới hạn này cho phép mô hình cung cấp câu trả lời dài h<PERSON>, nhưng nó cũng có thể làm tăng khả năng tạo ra nội dung không hữu ích hoặc không liên quan.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "T<PERSON><PERSON> chọn này sẽ xóa tất cả các tệp hiện có trong bộ sưu tập và thay thế chúng bằng các tệp mới được tải lên.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON><PERSON> hồi này đư<PERSON> tạo bởi \"{{model}}\"", "This will delete": "<PERSON>t này sẽ bị xóa", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON>ành động này sẽ xóa <strong>{{NAME}}</strong> và <strong>tất cả nội dung của nó</strong>.", "This will delete all models including custom models": "Hành động này sẽ xóa tất cả các mô hình bao gồm cả các mô hình tùy chỉnh", "This will delete all models including custom models and cannot be undone.": "Hành động này sẽ xóa tất cả các mô hình bao gồm cả các mô hình tùy chỉnh và không thể hoàn tác.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Hành động này sẽ đặt lại cơ sở kiến thức và đồng bộ hóa tất cả các tệp. Bạn có muốn tiếp tục không?", "Thorough explanation": "<PERSON><PERSON><PERSON><PERSON> thích k<PERSON> lưỡng", "Thought for {{DURATION}}": "<PERSON><PERSON> <PERSON><PERSON><PERSON> trong {{DURATION}}", "Thought for {{DURATION}} seconds": "<PERSON><PERSON> <PERSON>h<PERSON> trong {{DURATION}} gi<PERSON>y", "Thought for less than a second": "", "Thread": "<PERSON><PERSON><PERSON>", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON><PERSON> nhập URL cho Tika Server ", "Tiktoken": "Tiktoken", "Title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "Title (e.g. Tell me a fun fact)": "Ti<PERSON>u đề (ví dụ: <PERSON><PERSON><PERSON> kể cho tôi một sự thật thú vị về...)", "Title Auto-Generation": "Tự động Tạo Tiêu đề", "Title cannot be an empty string.": "Tiêu đề không được phép bỏ trống", "Title Generation": "<PERSON><PERSON><PERSON> đ<PERSON>", "Title Generation Prompt": "Prompt tạo tiêu đề", "TLS": "TLS", "To access the available model names for downloading,": "<PERSON><PERSON> truy cập tên các mô hình có sẵn để tải xuống,", "To access the GGUF models available for downloading,": "<PERSON><PERSON> truy cập các mô hình GGUF có sẵn để tải xuống,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "<PERSON><PERSON> truy cập vui lòng liên hệ với quản trị viên.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "<PERSON><PERSON> đ<PERSON>h kèm cơ sở kiến thức tại đây, trư<PERSON><PERSON> tiên hãy thêm chúng vào không gian làm việc \"Kiến thức\".", "To learn more about available endpoints, visit our documentation.": "<PERSON><PERSON> tìm hiểu thêm về các điểm cuối có sẵn, h<PERSON><PERSON> truy cập tài liệu của chúng tôi.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "<PERSON><PERSON> bảo vệ quyền riêng tư của bạn, chỉ c<PERSON> x<PERSON><PERSON> hạ<PERSON>, <PERSON> mô hình, thẻ và siêu dữ liệu được chia sẻ từ phản hồi của bạn—nhật ký trò chuyện của bạn vẫn ở chế độ riêng tư và không được bao gồm.", "To select toolkits here, add them to the \"Tools\" workspace first.": "<PERSON><PERSON> chọn c<PERSON> took<PERSON>, bạn ph<PERSON>i thêm chúng vào workspace \"Tools\" trước.", "Toast notifications for new updates": "<PERSON><PERSON><PERSON><PERSON> báo nhanh cho các cập nhật mới", "Today": "<PERSON><PERSON><PERSON> nay", "Today at {{LOCALIZED_TIME}}": "", "Toggle search": "", "Toggle settings": "Bật/tắt cài đặt", "Toggle sidebar": "Bật/tắt thanh bên", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "<PERSON>u<PERSON> dài dòng", "Tool created successfully": "Tool đã đ<PERSON><PERSON><PERSON> tạo thành công", "Tool deleted successfully": "<PERSON><PERSON> đã bị x<PERSON>a", "Tool Description": "<PERSON><PERSON>", "Tool ID": "ID Tool", "Tool imported successfully": "<PERSON><PERSON> nhập tool thành công", "Tool Name": "<PERSON><PERSON><PERSON>", "Tool Servers": "<PERSON><PERSON><PERSON>", "Tool updated successfully": "<PERSON>l đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "Tools": "Tools", "Tools Access": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Tools are a function calling system with arbitrary code execution": "Tools là một hệ thống gọi function với việc thực thi mã tùy ý", "Tools Function Calling Prompt": "Prompt Gọi Function c<PERSON><PERSON>", "Tools have a function calling system that allows arbitrary code execution.": "<PERSON><PERSON><PERSON> có hệ thống gọi function cho phép thực thi mã tùy ý.", "Tools Public Sharing": "<PERSON>a sẻ Công khai Tools", "Top K": "Top K", "Top K Reranker": "Top K Reranker", "Transformers": "Transformers", "Trouble accessing Ollama?": "Gặp vấn đề khi truy cập <PERSON>?", "Trust Proxy Environment": "<PERSON> cậy <PERSON> trường Proxy", "Try adjusting your search or filter to find what you are looking for.": "", "Try Again": "", "TTS Model": "<PERSON>ô hình TTS", "TTS Settings": "Cài đặt Chuyển văn bản thành <PERSON> nói", "TTS Voice": "Giọng nói TTS", "Type": "<PERSON><PERSON><PERSON>", "Type here...": "", "Type Hugging Face Resolve (Download) URL": "Nhập URL Hugging Face Resolve (<PERSON><PERSON>i <PERSON>u<PERSON>)", "Uh-oh! There was an issue with the response.": "Ôi! <PERSON><PERSON> có sự cố với phản hồi.", "UI": "<PERSON><PERSON><PERSON>", "Unarchive All": "Bỏ lưu trữ Tất cả", "Unarchive All Archived Chats": "Bỏ lưu trữ Tất cả Chat Đã Lưu trữ", "Unarchive Chat": "Bỏ lưu tr<PERSON>", "Underline": "", "Unknown": "", "Unknown User": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Mở khóa nh<PERSON>ng b<PERSON>n", "Unpin": "Bỏ ghim", "Unravel secrets": "<PERSON><PERSON><PERSON> sáng tỏ những bí mật", "Unsupported file type.": "", "Untagged": "<PERSON><PERSON><PERSON> gắn thẻ", "Untitled": "", "Update": "<PERSON><PERSON><PERSON>", "Update and Copy Link": "Cập nhật và sao chép link", "Update for the latest features and improvements.": "<PERSON><PERSON><PERSON> nhật để có các tính năng và cải tiến mới nhất.", "Update password": "<PERSON><PERSON><PERSON> nh<PERSON>t mật kh<PERSON>u", "Updated": "<PERSON><PERSON> cập nh<PERSON>t", "Updated at": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "Updated At": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "<PERSON><PERSON><PERSON> cấp lên gói cấp phép để có các khả năng nâng cao, bao gồm chủ đề và thương hiệu tùy chỉnh, và hỗ trợ chuyên dụng.", "Upload": "<PERSON><PERSON><PERSON>", "Upload a GGUF model": "<PERSON><PERSON><PERSON> lên mô hình GGUF", "Upload Audio": "", "Upload directory": "<PERSON><PERSON><PERSON> m<PERSON> tải lên", "Upload files": "<PERSON><PERSON><PERSON> l<PERSON>n c<PERSON> t<PERSON>p", "Upload Files": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> lên m<PERSON> chủ", "Upload Model": "", "Upload Pipeline": "<PERSON><PERSON><PERSON> l<PERSON>", "Upload Progress": "T<PERSON><PERSON><PERSON> trình tải tệp lên hệ thống", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "", "URL": "URL", "URL is required": "", "URL Mode": "Chế độ URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Sử dụng '#' trong ô nhập prompt để tải và bao gồm kiến thức của bạn.", "Use groups to group your users and assign permissions.": "<PERSON>ử dụng nhóm để nhóm người dùng của bạn và gán quyền.", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "<PERSON>ư<PERSON>i sử dụng", "User": "<PERSON><PERSON><PERSON><PERSON> dùng", "User Groups": "", "User location successfully retrieved.": "<PERSON><PERSON> truy xuất thành công vị trí của người dùng.", "User menu": "", "User Webhooks": "Webhook Người dùng", "Username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "Users": "<PERSON>ư<PERSON>i sử dụng", "Uses DefaultAzureCredential to authenticate": "", "Uses OAuth 2.1 Dynamic Client Registration": "", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Sử dụng mô hình arena mặc định với tất cả các mô hình. Nhấp vào nút dấu cộng để thêm các mô hình tùy chỉnh.", "Valid time units:": "Đơn vị thời gian h<PERSON> lệ:", "Validate certificate": "", "Valves": "Valves", "Valves updated": "<PERSON><PERSON> cập n<PERSON><PERSON><PERSON>", "Valves updated successfully": "<PERSON><PERSON> cập nh<PERSON>t <PERSON> thành công", "variable": "<PERSON><PERSON><PERSON>", "Verify Connection": "<PERSON><PERSON><PERSON>", "Verify SSL Certificate": "", "Version": "<PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "<PERSON><PERSON><PERSON> bản {{selectedVersion}} của {{totalVersions}}", "View Replies": "<PERSON><PERSON> l<PERSON>", "View Result from **{{NAME}}**": "<PERSON><PERSON> quả từ **{{NAME}}**", "Visibility": "<PERSON><PERSON><PERSON> thị", "Vision": "", "vlm": "", "Voice": "<PERSON><PERSON><PERSON><PERSON> nói", "Voice Input": "<PERSON><PERSON><PERSON><PERSON> liệu bằng Giọng nói", "Voice mode": "", "Warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "Warning:": "Cảnh báo:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "C<PERSON>nh báo: <PERSON><PERSON><PERSON> t<PERSON>h năng này sẽ cho phép người dùng tải lên mã tùy ý trên máy chủ.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Cảnh báo: <PERSON><PERSON><PERSON> cập nhật hoặc thay đổi embedding model, bạn sẽ cần cập nhật lại tất cả tài liệu.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Cảnh báo: <PERSON><PERSON><PERSON><PERSON> thi <PERSON> cho phép thực thi mã tùy ý, gây ra rủi ro bảo mật nghiêm trọng—hãy tiến hành hết sức thận trọng.", "Web": "Web", "Web API": "Web API", "Web Loader Engine": "", "Web Search": "<PERSON><PERSON><PERSON> k<PERSON>", "Web Search Engine": "<PERSON><PERSON><PERSON> n<PERSON> k<PERSON> Web", "Web Search in Chat": "<PERSON><PERSON><PERSON> k<PERSON>m Web trong Chat", "Web Search Query Generation": "<PERSON><PERSON><PERSON> T<PERSON> vấn Tìm k<PERSON>ếm Web", "Webhook URL": "Webhook URL", "Webpage URL": "", "WebUI Settings": "Cài đặt WebUI", "WebUI URL": "URL WebUI", "WebUI will make requests to \"{{url}}\"": "WebUI sẽ thực hiện yêu cầu đến \"{{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI sẽ thực hiện yêu cầu đến \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI sẽ thực hiện yêu cầu đến \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Bạn đang cố gắng đạt được điều gì?", "What are you working on?": "Bạn đang làm gì vậy?", "What's New in": "Th<PERSON>ng tin mới về", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON> đư<PERSON><PERSON> bậ<PERSON>, mô hình sẽ phản hồi từng tin nhắn trò chuyện trong thời gian thực, tạo ra phản hồi ngay khi người dùng gửi tin nhắn. Chế độ này hữu ích cho các ứng dụng trò chuyện trự<PERSON> tiế<PERSON>, nh<PERSON><PERSON> có thể ảnh hưởng đến hiệu suất trên phần cứng chậm hơn.", "wherever you are": "bất cứ nơi nào bạn đang ở", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Cục bộ)", "Why?": "Tại sao?", "Widescreen Mode": "<PERSON><PERSON> độ màn hình rộng", "Width": "", "Won": "<PERSON><PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "<PERSON><PERSON>t động cùng với top-k. <PERSON><PERSON><PERSON> trị cao hơn (ví dụ: 0.95) sẽ dẫn đến văn bản đa dạng hơn, trong khi giá trị thấp hơn (ví dụ: 0.5) sẽ tạo ra văn bản tập trung và thận trọng hơn.", "Workspace": "<PERSON><PERSON><PERSON><PERSON> gian làm vi<PERSON>c", "Workspace Permissions": "<PERSON><PERSON><PERSON><PERSON> gian làm việc", "Write": "<PERSON><PERSON><PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON> viết một prompt (vd: Bạn là ai?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Vi<PERSON><PERSON> một tóm tắt trong vòng 50 từ cho [chủ đề hoặc từ khóa].", "Write something...": "<PERSON><PERSON><PERSON><PERSON> gì đó...", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "Viết nội dung system prompt của mô hình tại đây\nví dụ: Bạn là Mario trong Super Mario Bros và đang đóng vai trò trợ lý.", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON><PERSON><PERSON> qua", "Yesterday at {{LOCALIZED_TIME}}": "", "You": "Bạn", "You are currently using a trial license. Please contact support to upgrade your license.": "Bạn hiện đang sử dụng giấy phép dùng thử. <PERSON><PERSON> lòng liên hệ bộ phận hỗ trợ để nâng cấp giấy phép của bạn.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Bạn chỉ có thể trò chuyện với tối đa {{maxCount}} tệp cùng một lúc.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Bạn có thể cá nhân hóa các tương tác của mình với LLM bằng cách thêm bộ nhớ thông qua nút 'Quản lý' bên dư<PERSON>, là<PERSON> cho chúng hữu ích hơn và phù hợp với bạn hơn.", "You cannot upload an empty file.": "<PERSON><PERSON><PERSON> không thể tải lên một tệp trống.", "You do not have permission to send messages in this channel.": "", "You do not have permission to send messages in this thread.": "", "You do not have permission to upload files.": "<PERSON><PERSON><PERSON> không có quyền tải lên tệp.", "You have no archived conversations.": "Bạn chưa lưu trữ một nội dung chat nào", "You have shared this chat": "Bạn vừa chia sẻ chat này", "You're a helpful assistant.": "Bạn là một trợ lý hữu ích.", "You're now logged in.": "Bạn đã đăng nhập.", "Your Account": "", "Your account status is currently pending activation.": "<PERSON><PERSON><PERSON> khoản của bạn hiện đang ở trạng thái chờ kích hoạt.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Toàn bộ đóng góp của bạn sẽ được chuyển trực tiếp đến nhà phát triển plugin; Open WebUI không lấy bất kỳ tỷ lệ phần trăm nào. <PERSON><PERSON>, nền tảng được chọn tài trợ có thể có phí riêng.", "YouTube": "Youtube", "Youtube Language": "<PERSON><PERSON><PERSON> You<PERSON>e", "Youtube Proxy URL": "URL Proxy Youtube"}